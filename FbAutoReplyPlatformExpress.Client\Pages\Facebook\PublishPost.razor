@page "/facebook/publish-post"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Logging
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookPostPublishingService FacebookPostPublishingService
@inject IFacebookPageService FacebookPageService
@inject NavigationManager NavigationManager
@inject IMessageService MessageService
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Posts.Create)]

<PageTitle>Publish Facebook Post</PageTitle>

<Row>
    <Column ColumnSize="ColumnSize.Is12">
        <Card>
            <CardHeader>
                <Row Class="justify-content-between align-items-center">
                    <Column ColumnSize="ColumnSize.IsAuto">
                        <h2>
                            <Icon Name="IconName.Edit" class="me-2" />
                            Publish Facebook Post
                        </h2>
                    </Column>
                    <Column ColumnSize="ColumnSize.IsAuto">
                        <Button Color="Color.Secondary" Outline="true" Clicked="@(() => NavigationManager.NavigateTo("/facebook/pages"))">
                            <Icon Name="IconName.ArrowLeft" class="me-2" />
                            Back to Pages
                        </Button>
                    </Column>
                </Row>
            </CardHeader>
            <CardBody>
                @if (!IsLoading && !AvailablePages.Any())
                {
                    <!-- No Pages Available -->
                    <Alert Color="Color.Warning">
                        <Icon Name="IconName.ExclamationTriangle" class="me-2" />
                        <strong>No Facebook Pages Available</strong>
                        <p class="mb-0 mt-2">
                            You need to connect your Facebook account and import pages before you can publish posts.
                            <a href="/facebook/connection" class="alert-link">Connect Facebook Account</a>
                        </p>
                    </Alert>
                }
                else if (IsLoading)
                {
                    <!-- Loading State -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Loading Facebook pages...</p>
                    </div>
                }
                else
                {
                    <!-- Error Handler -->
                    @if (PublishResult != null && !PublishResult.Success)
                    {
                        <PostPublishingErrorHandler PublishResult="PublishResult"
                                                  OnReconnectClicked="HandleReconnectClicked"
                                                  OnRetryClicked="RetryPublishingAsync"
                                                  OnEditClicked="HandleEditClicked"
                                                  OnDismissClicked="HandleErrorDismissed"
                                                  ShowTechnicalDetails="true" />
                    }

                    <!-- Main Publishing Interface -->
                    <Row>
                        <Column ColumnSize="ColumnSize.Is8">
                            <!-- Page Selection -->
                            <Card class="mb-4">
                                <CardHeader>
                                    <h5 class="mb-0">
                                        <Icon Name="IconName.FileAlt" class="me-2" />
                                        Select Facebook Page
                                    </h5>
                                </CardHeader>
                                <CardBody>
                                    <Field>
                                        <FieldLabel>Facebook Page</FieldLabel>
                                        <Select TValue="Guid?" @bind-Value="PostData.FacebookPageId" @onchange="OnPageSelectionChanged">
                                            <SelectItem Value="@((Guid?)null)">-- Select a Facebook Page --</SelectItem>
                                            @foreach (var page in AvailablePages)
                                            {
                                                <SelectItem Value="page.Id">
                                                    @(page.PageName) (@(page.Category))
                                                </SelectItem>
                                            }
                                        </Select>
                                        <FieldHelp>Choose which Facebook page to publish this post to</FieldHelp>
                                    </Field>
                                </CardBody>
                            </Card>

                            <!-- Post Composer -->
                            <PostComposer @bind-PostData="PostData" OnContentChanged="OnPostContentChanged" />

                            <!-- Post Scheduler -->
                            <div class="mt-4">
                                <PostScheduler @bind-PublishNow="PostData.PublishNow" 
                                             @bind-ScheduledPublishTime="PostData.ScheduledPublishTime"
                                             OnScheduleChanged="OnScheduleChanged" />
                            </div>

                            <!-- Publishing Actions -->
                            <Card class="mt-4">
                                <CardBody>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            @if (!string.IsNullOrEmpty(PublishingStatus))
                                            {
                                                <span class="text-muted">@PublishingStatus</span>
                                            }
                                        </div>
                                        <div class="d-flex gap-2">
                                            <Button Color="Color.Secondary" 
                                                   Outline="true" 
                                                   Clicked="ClearForm"
                                                   Disabled="IsPublishing">
                                                <Icon Name="IconName.Delete" class="me-2" />
                                                Clear
                                            </Button>
                                            <Button Color="Color.Primary" 
                                                   Clicked="PublishPostAsync"
                                                   Disabled="@(!CanPublish || IsPublishing)"
                                                   Loading="IsPublishing">
                                                <Icon Name="@(PostData.PublishNow ? IconName.Play : IconName.Clock)" class="me-2" />
                                                @(PostData.PublishNow ? "Publish Now" : "Schedule Post")
                                            </Button>
                                        </div>
                                    </div>
                                </CardBody>
                            </Card>
                        </Column>

                        <!-- Preview Column -->
                        <Column ColumnSize="ColumnSize.Is4">
                            <div class="sticky-top" style="top: 20px;">
                                <h5 class="mb-3">
                                    <Icon Name="IconName.Eye" class="me-2" />
                                    Preview
                                </h5>
                                
                                @if (PostData.FacebookPageId != Guid.Empty && PostPreview != null)
                                {
                                    <PostPreview PreviewData="PostPreview" 
                                               IsScheduled="!PostData.PublishNow"
                                               ScheduledTime="PostData.ScheduledPublishTime" />
                                }
                                else
                                {
                                    <Card>
                                        <CardBody class="text-center text-muted py-5">
                                            <Icon Name="IconName.Eye" Size="IconSize.x3" class="mb-3" />
                                            <p>Select a page and add content to see preview</p>
                                        </CardBody>
                                    </Card>
                                }
                            </div>
                        </Column>
                    </Row>
                }
            </CardBody>
        </Card>
    </Column>
</Row>

@code {
    private PublishFacebookPostDto PostData = new();
    private List<FacebookPageDto> AvailablePages = new();
    private FacebookPostPreviewDto? PostPreview;
    private FacebookPostPublishResultDto? PublishResult;
    private bool IsLoading = true;
    private bool IsPublishing = false;
    private string PublishingStatus = "";

    private bool CanPublish => PostData.FacebookPageId != Guid.Empty &&
                              PostData.IsValid &&
                              (!PostData.PublishNow ? PostData.ScheduledPublishTime.HasValue : true);

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await LoadAvailablePagesAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading Facebook pages");
            await MessageService.Error("Failed to load Facebook pages. Please try again.");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadAvailablePagesAsync()
    {
        AvailablePages = await FacebookPostPublishingService.GetAvailablePagesForPostingAsync();
    }

    private async Task OnPageSelectionChanged()
    {
        await UpdatePreviewAsync();
    }

    private async Task OnPostContentChanged()
    {
        await UpdatePreviewAsync();
    }

    private async Task OnScheduleChanged()
    {
        await UpdatePreviewAsync();
    }

    private async Task UpdatePreviewAsync()
    {
        if (PostData.FacebookPageId != Guid.Empty)
        {
            try
            {
                PostPreview = await FacebookPostPublishingService.GeneratePostPreviewAsync(PostData);
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Error generating post preview");
                PostPreview = null;
            }
        }
        else
        {
            PostPreview = null;
        }
        
        StateHasChanged();
    }

    private async Task PublishPostAsync()
    {
        if (!CanPublish) return;

        IsPublishing = true;
        PublishingStatus = PostData.PublishNow ? "Publishing post..." : "Scheduling post...";
        PublishResult = null; // Clear previous errors
        StateHasChanged();

        try
        {
            var result = await FacebookPostPublishingService.PublishPostAsync(PostData);
            PublishResult = result;

            if (result.Success)
            {
                var message = PostData.PublishNow
                    ? "Post published successfully!"
                    : $"Post scheduled successfully for {PostData.ScheduledPublishTime:MMM d, yyyy 'at' h:mm tt}";

                await MessageService.Success(message);

                // Clear form and navigate
                ClearForm();
                NavigationManager.NavigateTo("/facebook/pages");
            }
            else
            {
                // Error will be displayed by PostPublishingErrorHandler component
                Logger.LogWarning("Post publishing failed: {ErrorType} - {ErrorMessage}", result.ErrorType, result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error publishing Facebook post");

            // Create a generic error result for unexpected exceptions
            PublishResult = new FacebookPostPublishResultDto
            {
                Success = false,
                ErrorType = FacebookErrorType.General,
                ErrorMessage = $"An unexpected error occurred: {ex.Message}"
            };
        }
        finally
        {
            IsPublishing = false;
            PublishingStatus = "";
            StateHasChanged();
        }
    }

    private void ClearForm()
    {
        PostData = new PublishFacebookPostDto();
        PostPreview = null;
        PublishResult = null;
        PublishingStatus = "";
        StateHasChanged();
    }

    private async Task RetryPublishingAsync()
    {
        await PublishPostAsync();
    }

    private async Task HandleReconnectClicked()
    {
        NavigationManager.NavigateTo("/facebook/connection");
    }

    private async Task HandleEditClicked()
    {
        // Just dismiss the error to allow editing
        PublishResult = null;
        StateHasChanged();
    }

    private async Task HandleErrorDismissed()
    {
        PublishResult = null;
        StateHasChanged();
    }
}
