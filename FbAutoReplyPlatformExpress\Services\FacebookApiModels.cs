using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace FbAutoReplyPlatformExpress.Services;

public static class FacebookPostTypes
{
    public const string Status = "status";
    public const string Photo = "photo";
    public const string Video = "video";
    public const string VideoReel = "video_reel";
    public const string Album = "album";
    public const string Link = "link";
}

public class FacebookUserInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("picture")]
    public FacebookPicture? Picture { get; set; }
}

public class FacebookPicture
{
    [JsonProperty("data")]
    public FacebookPictureData? Data { get; set; }
}

public class FacebookPictureData
{
    [JsonProperty("url")]
    public string Url { get; set; } = string.Empty;
}

public class FacebookPageInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("access_token")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonProperty("picture")]
    public FacebookPicture? Picture { get; set; }

    [JsonProperty("category")]
    public string? Category { get; set; }

    [JsonProperty("fan_count")]
    public int FanCount { get; set; }
}

public class FacebookPagesResponse
{
    [JsonProperty("data")]
    public List<FacebookPageInfo>? Data { get; set; }
}

public class FacebookPostInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("created_time")]
    public DateTime CreatedTime { get; set; }

    [JsonProperty("attachments")]
    public FacebookAttachments? Attachments { get; set; }

    [JsonProperty("likes")]
    public FacebookSummary? Likes { get; set; }

    [JsonProperty("comments")]
    public FacebookSummary? Comments { get; set; }

    [JsonProperty("shares")]
    public FacebookShares? Shares { get; set; }

    [JsonProperty("permalink_url")]
    public string? PermalinkUrl { get; set; }

    [JsonProperty("picture")]
    public string? Picture { get; set; }

    [JsonProperty("full_picture")]
    public string? FullPicture { get; set; }

    // Helper property to indicate if this is a Video Reel
    public bool IsVideoReel { get; set; } = false;

    // Static method to convert VideoReelInfo to PostInfo for unified handling
    public static FacebookPostInfo FromVideoReel(FacebookVideoReelInfo videoReel)
    {
        return new FacebookPostInfo
        {
            Id = videoReel.PostId,
            Message = videoReel.Description,
            CreatedTime = videoReel.CreatedTime,
            Likes = videoReel.Likes,
            Comments = videoReel.Comments,
            Shares = videoReel.Shares,
            PermalinkUrl = videoReel.PermalinkUrl,
            Picture = videoReel.Picture ?? videoReel.Thumbnails?.Data?.FirstOrDefault()?.Uri,
            FullPicture = videoReel.Picture ?? videoReel.Thumbnails?.Data?.FirstOrDefault()?.Uri,
            IsVideoReel = true
        };
    }
}

public class FacebookAttachments
{
    [JsonProperty("data")]
    public List<FacebookAttachment>? Data { get; set; }
}

public class FacebookAttachment
{
    [JsonProperty("media")]
    public FacebookMedia? Media { get; set; }

    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("url")]
    public string? Url { get; set; }
}

public class FacebookMedia
{
    [JsonProperty("image")]
    public FacebookImage? Image { get; set; }
}

public class FacebookImage
{
    [JsonProperty("src")]
    public string Src { get; set; } = string.Empty;
}

public class FacebookSummary
{
    [JsonProperty("summary")]
    public FacebookSummaryData? Summary { get; set; }
}

public class FacebookSummaryData
{
    [JsonProperty("total_count")]
    public int TotalCount { get; set; }
}

public class FacebookShares
{
    [JsonProperty("count")]
    public int Count { get; set; }
}

public class FacebookPostsResponse
{
    [JsonProperty("data")]
    public List<FacebookPostInfo>? Data { get; set; }
}

public class FacebookVideoReelInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("post_id")]
    public string PostId { get; set; } = string.Empty;

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("created_time")]
    public DateTime CreatedTime { get; set; }

    [JsonProperty("permalink_url")]
    public string? PermalinkUrl { get; set; }

    [JsonProperty("picture")]
    public string? Picture { get; set; }

    [JsonProperty("source")]
    public string? Source { get; set; }

    [JsonProperty("title")]
    public string? Title { get; set; }

    [JsonProperty("likes")]
    public FacebookSummary? Likes { get; set; }

    [JsonProperty("comments")]
    public FacebookSummary? Comments { get; set; }

    [JsonProperty("shares")]
    public FacebookShares? Shares { get; set; }

    [JsonProperty("length")]
    public double? Length { get; set; }

    [JsonProperty("thumbnails")]
    public FacebookVideoThumbnails? Thumbnails { get; set; }
}

public class FacebookVideoThumbnails
{
    [JsonProperty("data")]
    public List<FacebookVideoThumbnail>? Data { get; set; }
}

public class FacebookVideoThumbnail
{
    [JsonProperty("uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonProperty("width")]
    public int Width { get; set; }

    [JsonProperty("height")]
    public int Height { get; set; }
}

public class FacebookVideoReelsResponse
{
    [JsonProperty("data")]
    public List<FacebookVideoReelInfo>? Data { get; set; }
}

public class FacebookTokenResponse
{
    [JsonProperty("access_token")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonProperty("token_type")]
    public string TokenType { get; set; } = string.Empty;

    [JsonProperty("expires_in")]
    public int? ExpiresIn { get; set; }
}

public class FacebookCommentResponse
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;
}

public class FacebookMessageResponse
{
    [JsonProperty("message_id")]
    public string MessageId { get; set; } = string.Empty;
}

public class FacebookWebhookEntry
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("time")]
    public long Time { get; set; }

    [JsonProperty("changes")]
    public List<FacebookWebhookChange>? Changes { get; set; }
}

public class FacebookWebhookChange
{
    [JsonProperty("field")]
    public string Field { get; set; } = string.Empty;

    [JsonProperty("value")]
    public FacebookWebhookValue? Value { get; set; }
}

public class FacebookWebhookValue
{
    [JsonProperty("item")]
    public string Item { get; set; } = string.Empty;

    [JsonProperty("comment_id")]
    public string? CommentId { get; set; }

    [JsonProperty("post_id")]
    public string? PostId { get; set; }

    [JsonProperty("parent_id")]
    public string? ParentId { get; set; }

    [JsonProperty("sender_id")]
    public string? SenderId { get; set; }

    [JsonProperty("sender_name")]
    public string? SenderName { get; set; }

    [JsonProperty("from")]
    public FacebookCommentSender? From { get; set; }

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("created_time")]
    public long? CreatedTime { get; set; }

    [JsonProperty("verb")]
    public string? Verb { get; set; }
}

public class FacebookWebhookPayload
{
    [JsonProperty("object")]
    public string Object { get; set; } = string.Empty;

    [JsonProperty("entry")]
    public List<FacebookWebhookEntry>? Entry { get; set; }
}

public class FacebookTokenDebugResponse
{
    [JsonProperty("data")]
    public FacebookTokenDebugData? Data { get; set; }
}

public class FacebookTokenDebugData
{
    [JsonProperty("app_id")]
    public string AppId { get; set; } = string.Empty;

    [JsonProperty("type")]
    public string Type { get; set; } = string.Empty;

    [JsonProperty("application")]
    public string Application { get; set; } = string.Empty;

    [JsonProperty("data_access_expires_at")]
    public long? DataAccessExpiresAt { get; set; }

    [JsonProperty("expires_at")]
    public long? ExpiresAt { get; set; }

    [JsonProperty("is_valid")]
    public bool IsValid { get; set; }

    [JsonProperty("scopes")]
    public List<string>? Scopes { get; set; }

    [JsonProperty("user_id")]
    public string? UserId { get; set; }

    [JsonProperty("error")]
    public FacebookTokenError? Error { get; set; }
}

public class FacebookTokenError
{
    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; } = string.Empty;

    [JsonProperty("subcode")]
    public int? Subcode { get; set; }
}

public class FacebookTokenValidationResult
{
    public bool IsValid { get; set; }
    public bool IsExpired { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public List<string> Scopes { get; set; } = new();
    public List<string> MissingScopes { get; set; } = new();
    public string? ErrorMessage { get; set; }
    public int? ErrorCode { get; set; }
    public string TokenType { get; set; } = string.Empty;
}

public class FacebookPermissionRevocationResponse
{
    [JsonProperty("success")]
    public bool Success { get; set; }
}

public class FacebookCommentSender
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }
}

public class FacebookCommentInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("message")]
    public string Message { get; set; } = string.Empty;

    [JsonProperty("created_time")]
    public DateTime CreatedTime { get; set; }

    [JsonProperty("from")]
    public FacebookCommentSender? From { get; set; }
}

public class FacebookCommentsResponse
{
    [JsonProperty("data")]
    public List<FacebookCommentInfo>? Data { get; set; }

    [JsonProperty("paging")]
    public FacebookPaging? Paging { get; set; }
}

public class FacebookPaging
{
    [JsonProperty("cursors")]
    public FacebookCursors? Cursors { get; set; }

    [JsonProperty("next")]
    public string? Next { get; set; }

    [JsonProperty("previous")]
    public string? Previous { get; set; }
}

public class FacebookCursors
{
    [JsonProperty("before")]
    public string? Before { get; set; }

    [JsonProperty("after")]
    public string? After { get; set; }
}
