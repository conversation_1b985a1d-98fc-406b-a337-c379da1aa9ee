﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FbAutoReplyPlatformExpress.Migrations
{
    /// <inheritdoc />
    public partial class AddFacebookEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppFacebookUsers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FacebookId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccessToken = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    RefreshToken = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    TokenExpiresAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    FacebookEmail = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    FacebookName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    ProfilePictureUrl = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppFacebookUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppFacebookUsers_AbpUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AbpUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AppFacebookPages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FacebookPageId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    FacebookUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PageName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    PageAccessToken = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    PageProfilePictureUrl = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    Category = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    FollowersCount = table.Column<int>(type: "int", nullable: false),
                    IsConnected = table.Column<bool>(type: "bit", nullable: false),
                    WebhookSubscribed = table.Column<bool>(type: "bit", nullable: false),
                    WebhookSubscriptionId = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    LastSyncAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppFacebookPages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppFacebookPages_AppFacebookUsers_FacebookUserId",
                        column: x => x.FacebookUserId,
                        principalTable: "AppFacebookUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AppFacebookPosts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FacebookPostId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    FacebookPageId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(2048)", maxLength: 2048, nullable: false),
                    PostType = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    AttachmentUrl = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    LinkUrl = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    FacebookCreatedTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    LikesCount = table.Column<int>(type: "int", nullable: false),
                    CommentsCount = table.Column<int>(type: "int", nullable: false),
                    SharesCount = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    LastSyncAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppFacebookPosts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppFacebookPosts_AppFacebookPages_FacebookPageId",
                        column: x => x.FacebookPageId,
                        principalTable: "AppFacebookPages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AppAutoReplyCampaigns",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FacebookPostId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CampaignName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    PublicReplyMessage = table.Column<string>(type: "nvarchar(2048)", maxLength: 2048, nullable: false),
                    PrivateReplyMessage = table.Column<string>(type: "nvarchar(2048)", maxLength: 2048, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    SendPublicReply = table.Column<bool>(type: "bit", nullable: false),
                    SendPrivateReply = table.Column<bool>(type: "bit", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MaxRepliesPerUser = table.Column<int>(type: "int", nullable: false),
                    TotalRepliesSent = table.Column<int>(type: "int", nullable: false),
                    PublicRepliesSent = table.Column<int>(type: "int", nullable: false),
                    PrivateRepliesSent = table.Column<int>(type: "int", nullable: false),
                    LastReplyAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppAutoReplyCampaigns", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppAutoReplyCampaigns_AppFacebookPosts_FacebookPostId",
                        column: x => x.FacebookPostId,
                        principalTable: "AppFacebookPosts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AppCampaignActivities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CampaignId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FacebookCommentId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    CommenterFacebookId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    CommenterName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    OriginalComment = table.Column<string>(type: "nvarchar(2048)", maxLength: 2048, nullable: false),
                    PublicReplyMessage = table.Column<string>(type: "nvarchar(2048)", maxLength: 2048, nullable: true),
                    PrivateReplyMessage = table.Column<string>(type: "nvarchar(2048)", maxLength: 2048, nullable: true),
                    PublicReplyId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    PrivateMessageId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    PublicReplySent = table.Column<bool>(type: "bit", nullable: false),
                    PrivateReplySent = table.Column<bool>(type: "bit", nullable: false),
                    PublicReplyAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PrivateReplyAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ErrorMessage = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: true),
                    HasError = table.Column<bool>(type: "bit", nullable: false),
                    RetryCount = table.Column<int>(type: "int", nullable: false),
                    CommentCreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppCampaignActivities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppCampaignActivities_AppAutoReplyCampaigns_CampaignId",
                        column: x => x.CampaignId,
                        principalTable: "AppAutoReplyCampaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppAutoReplyCampaigns_FacebookPostId",
                table: "AppAutoReplyCampaigns",
                column: "FacebookPostId");

            migrationBuilder.CreateIndex(
                name: "IX_AppCampaignActivities_CampaignId",
                table: "AppCampaignActivities",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_AppCampaignActivities_CommenterFacebookId",
                table: "AppCampaignActivities",
                column: "CommenterFacebookId");

            migrationBuilder.CreateIndex(
                name: "IX_AppCampaignActivities_FacebookCommentId",
                table: "AppCampaignActivities",
                column: "FacebookCommentId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppFacebookPages_FacebookPageId",
                table: "AppFacebookPages",
                column: "FacebookPageId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppFacebookPages_FacebookUserId",
                table: "AppFacebookPages",
                column: "FacebookUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AppFacebookPosts_FacebookPageId",
                table: "AppFacebookPosts",
                column: "FacebookPageId");

            migrationBuilder.CreateIndex(
                name: "IX_AppFacebookPosts_FacebookPostId",
                table: "AppFacebookPosts",
                column: "FacebookPostId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppFacebookUsers_FacebookId",
                table: "AppFacebookUsers",
                column: "FacebookId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppFacebookUsers_UserId",
                table: "AppFacebookUsers",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppCampaignActivities");

            migrationBuilder.DropTable(
                name: "AppAutoReplyCampaigns");

            migrationBuilder.DropTable(
                name: "AppFacebookPosts");

            migrationBuilder.DropTable(
                name: "AppFacebookPages");

            migrationBuilder.DropTable(
                name: "AppFacebookUsers");
        }
    }
}
