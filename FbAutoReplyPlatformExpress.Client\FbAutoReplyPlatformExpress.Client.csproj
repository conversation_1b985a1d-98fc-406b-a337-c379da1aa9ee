<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>FbAutoReplyPlatformExpress</RootNamespace>
    <BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
  </PropertyGroup>

	<ItemGroup>
    <ProjectReference Include="..\FbAutoReplyPlatformExpress.Contracts\FbAutoReplyPlatformExpress.Contracts.csproj" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.6.2" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.6.2" />
    <PackageReference Include="Blazorise.Components" Version="1.6.2" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.0" />
	</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Autofac.WebAssembly" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXTheme" Version="4.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.Pro.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.TextTemplateManagement.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AuditLogging.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.LanguageManagement.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>

  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.SettingManagement.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.FeatureManagement.Blazor.WebAssembly" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi.Client" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="$(UserProfile)\.nuget\packages\*\*\contentFiles\any\*\*.abppkg*" />
  </ItemGroup>

</Project>
