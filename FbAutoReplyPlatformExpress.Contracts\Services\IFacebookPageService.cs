using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace FbAutoReplyPlatformExpress.Services;

public interface IFacebookPageService : IApplicationService
{
    Task<PagedResultDto<FacebookPageDto>> GetListAsync(PagedAndSortedResultRequestDto input);
    Task<FacebookPageDto> GetAsync(Guid id);
    Task<FacebookPageDto> CreateAsync(CreateFacebookPageDto input);
    Task<FacebookPageDto> UpdateAsync(Guid id, UpdateFacebookPageDto input);
    Task DeleteAsync(Guid id);
    
    Task<List<FacebookPageImportDto>> GetAvailablePagesFromFacebookAsync();
    Task<FacebookPageDto> ImportPageAsync(FacebookPageImportDto input);
    Task<List<FacebookPageDto>> ImportMultiplePagesAsync(List<FacebookPageImportDto> input);
    
    Task SubscribeToWebhookAsync(Guid pageId);
    Task UnsubscribeFromWebhookAsync(Guid pageId);
    Task SyncPageInfoAsync(Guid pageId);
    Task SyncAllPagesAsync();

    Task DisconnectPageAsync(Guid pageId);
    Task ReconnectPageAsync(Guid pageId);
    Task RefreshAllPageTokensAsync();
    Task RefreshAllPageTokensForUserAsync(FacebookUserDto facebookUser);
}
