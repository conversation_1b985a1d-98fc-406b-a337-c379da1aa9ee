@page "/campaigns/select-post"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Application.Dtos
@using Microsoft.JSInterop
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookPostService FacebookPostService
@inject IFacebookPageService FacebookPageService
@inject IAutoReplyCampaignService CampaignService
@inject IUiMessageService UiMessage
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<Card>
    <CardHeader>
        <Row>
            <Column ColumnSize="ColumnSize.Is8">
                <h4>
                    <Icon Name="IconName.Add" />
                    Select Facebook Post for Campaign
                </h4>
                <p class="text-muted mb-0">Choose a Facebook post to create an auto-reply campaign</p>
            </Column>
            <Column ColumnSize="ColumnSize.Is4" Class="text-end">
                <Button Color="Color.Secondary" Clicked="NavigateBack">
                    <Icon Name="IconName.ArrowLeft" />
                    Back to Campaigns
                </Button>
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        <!-- Filters -->
        <Card Class="mb-3">
            <CardBody>
                <Row>
                    <Column ColumnSize="ColumnSize.Is4">
                        <Field>
                            <FieldLabel>Facebook Page</FieldLabel>
                            <Select TValue="Guid?" @bind-SelectedValue="SelectedPageId" @onchange="OnPageFilterChanged">
                                <SelectItem TValue="Guid?" Value="null">All Pages</SelectItem>
                                @if (FacebookPages != null)
                                {
                                    @foreach (var pageItem in FacebookPages)
                                    {
                                        <SelectItem TValue="Guid?" Value="pageItem.Id">@pageItem.PageName</SelectItem>
                                    }
                                }
                            </Select>
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is4">
                        <Field>
                            <FieldLabel>Post Limit</FieldLabel>
                            <Select TValue="int" @bind-SelectedValue="PostLimit" @onchange="OnLimitChanged">
                                <SelectItem TValue="int" Value="25">25 Posts</SelectItem>
                                <SelectItem TValue="int" Value="50">50 Posts</SelectItem>
                                <SelectItem TValue="int" Value="100">100 Posts</SelectItem>
                            </Select>
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is4" Class="d-flex align-items-end">
                        <Button Color="Color.Primary" Clicked="LoadPostsAsync" Disabled="IsLoading">
                            @if (IsLoading)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <Icon Name="IconName.Sync" />
                            Refresh Posts
                        </Button>
                    </Column>
                </Row>
            </CardBody>
        </Card>

        @if (IsLoading)
        {
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading posts...</span>
                </div>
                <p class="mt-3 text-muted">Fetching latest Facebook posts...</p>
            </div>
        }
        else if (!IsConnected)
        {
            <Alert Color="Color.Warning" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                <strong>Facebook Not Connected</strong>
                <p class="mb-0">Please connect your Facebook account first to view posts.</p>
                <Button Color="Color.Primary" Class="mt-2" Clicked="@(() => NavigationManager.NavigateTo("/facebook/connection"))">
                    Connect Facebook
                </Button>
            </Alert>
        }
        else if (FacebookPosts == null || !FacebookPosts.Any())
        {
            <Alert Color="Color.Info" Visible="true">
                <Icon Name="IconName.Info" />
                <strong>No Posts Found</strong>
                <p class="mb-0">No Facebook posts found. Try refreshing or check your page connections.</p>
            </Alert>
        }
        else
        {
            <!-- Posts Grid -->
            <Row>
                @foreach (var post in FacebookPosts)
                {
                    <Column ColumnSize="ColumnSize.Is12.Is6.OnTablet.Is4.OnDesktop" Class="mb-4">
                        <Card Class="@($"h-100 {(post.HasActiveCampaign ? "border-warning" : "")}")">
                            @if (!string.IsNullOrEmpty(post.PictureUrl) || !string.IsNullOrEmpty(post.FullPictureUrl))
                            {
                                <div style="height: 200px; overflow: hidden; position: relative;">
                                    <img src="@(post.FullPictureUrl ?? post.PictureUrl)"
                                         alt="@(post.IsVideoReel ? "Video Reel" : "Post image")"
                                         class="card-img-top"
                                         style="width: 100%; height: 100%; object-fit: cover;" />

                                    <!-- Post Type Badge -->
                                    <div style="position: absolute; top: 8px; right: 8px;">
                                        @if (post.IsVideoReel)
                                        {
                                            <Badge Color="Color.Primary" Class="d-flex align-items-center gap-1">
                                                <Icon Name="IconName.Video" Style="font-size: 12px;" />
                                                <span style="font-size: 11px;">Reel</span>
                                            </Badge>
                                        }
                                        else if (post.PostType == "video")
                                        {
                                            <Badge Color="Color.Info" Class="d-flex align-items-center gap-1">
                                                <Icon Name="IconName.Play" Style="font-size: 12px;" />
                                                <span style="font-size: 11px;">Video</span>
                                            </Badge>
                                        }
                                        else if (post.PostType == "photo")
                                        {
                                            <Badge Color="Color.Success" Class="d-flex align-items-center gap-1">
                                                <Icon Name="IconName.Image" Style="font-size: 12px;" />
                                                <span style="font-size: 11px;">Photo</span>
                                            </Badge>
                                        }
                                    </div>
                                </div>
                            }
                            <CardBody Class="d-flex flex-column">
                                <!-- Page Info -->
                                <div class="d-flex align-items-center justify-content-between mb-2">
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(post.PageProfilePictureUrl))
                                        {
                                            <img src="@post.PageProfilePictureUrl"
                                                 alt="@post.PageName"
                                                 class="rounded-circle me-2"
                                                 style="width: 24px; height: 24px;" />
                                        }
                                        <small class="text-muted fw-bold">@post.PageName</small>
                                    </div>

                                    <!-- Post Type Badge for posts without images -->
                                    @if (string.IsNullOrEmpty(post.PictureUrl) && string.IsNullOrEmpty(post.FullPictureUrl))
                                    {
                                        @if (post.IsVideoReel)
                                        {
                                            <Badge Color="Color.Primary" Class="d-flex align-items-center gap-1">
                                                <Icon Name="IconName.Video" Style="font-size: 12px;" />
                                                <span style="font-size: 11px;">Reel</span>
                                            </Badge>
                                        }
                                        else if (post.PostType == "video")
                                        {
                                            <Badge Color="Color.Info" Class="d-flex align-items-center gap-1">
                                                <Icon Name="IconName.Play" Style="font-size: 12px;" />
                                                <span style="font-size: 11px;">Video</span>
                                            </Badge>
                                        }
                                        else if (post.PostType == "status")
                                        {
                                            <Badge Color="Color.Secondary" Class="d-flex align-items-center gap-1">
                                                <Icon Name="IconName.File" Style="font-size: 12px;" />
                                                <span style="font-size: 11px;">Text</span>
                                            </Badge>
                                        }
                                    }
                                </div>

                                <!-- Post Content -->
                                <div class="mb-3 flex-grow-1">
                                    @if (!string.IsNullOrEmpty(post.Message))
                                    {
                                        <p class="card-text">
                                            @(post.Message.Length > 150 ? post.Message.Substring(0, 150) + "..." : post.Message)
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="card-text text-muted fst-italic">No text content</p>
                                    }
                                </div>

                                <!-- Post Stats -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex gap-3">
                                        <small class="text-muted">
                                            <Icon Name="IconName.Heart" /> @post.LikesCount
                                        </small>
                                        <small class="text-muted">
                                            <Icon Name="IconName.Comment" /> @post.CommentsCount
                                        </small>
                                        <small class="text-muted">
                                            <Icon Name="IconName.Share" /> @post.SharesCount
                                        </small>
                                    </div>
                                    <small class="text-muted">@post.FacebookCreatedTime.ToString("MMM dd, yyyy")</small>
                                </div>

                                <!-- Campaign Status & Actions -->
                                @if (post.HasActiveCampaign)
                                {
                                    <Alert Color="Color.Warning" Visible="true" Class="mb-2 py-2">
                                        <small>
                                            <Icon Name="IconName.ExclamationTriangle" />
                                            Active campaign exists
                                        </small>
                                    </Alert>
                                }

                                <div class="d-flex gap-2">
                                    @if (post.HasActiveCampaign)
                                    {
                                        <Button Color="Color.Warning"
                                                Size="Size.Small"
                                                Clicked="() => EditCampaignAsync(post)"
                                                Class="flex-fill">
                                            <Icon Name="IconName.Edit" />
                                            Edit Campaign
                                        </Button>
                                    }
                                    else
                                    {
                                        <Button Color="Color.Success"
                                                Size="Size.Small"
                                                Clicked="() => CreateCampaignAsync(post)"
                                                Class="flex-fill">
                                            <Icon Name="IconName.Add" />
                                            Create Campaign
                                        </Button>
                                    }
                                    @if (!string.IsNullOrEmpty(post.PermalinkUrl))
                                    {
                                        <Button Color="Color.Secondary"
                                                Size="Size.Small"
                                                Clicked="() => OpenFacebookPostAsync(post.PermalinkUrl)">
                                            <Icon Name="IconName.Link" />
                                        </Button>
                                    }
                                </div>
                            </CardBody>
                        </Card>
                    </Column>
                }
            </Row>
        }
    </CardBody>
</Card>

@code {
    private List<FacebookPostSelectionDto>? FacebookPosts;
    private List<FacebookPageDto>? FacebookPages;
    private bool IsLoading = false;
    private bool IsConnected = false;
    private int PostLimit = 50;

    // Filters
    private Guid? SelectedPageId;

    [Parameter]
    [SupplyParameterFromQuery]
    public Guid? PageId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await CheckConnectionStatusAsync();
        if (IsConnected)
        {
            await LoadPagesAsync();
            if (PageId.HasValue)
            {
                SelectedPageId = PageId.Value;
            }
            await LoadPostsAsync();
        }
    }

    private async Task CheckConnectionStatusAsync()
    {
        try
        {
            var pages = await FacebookPageService.GetListAsync(new PagedAndSortedResultRequestDto { MaxResultCount = 1 });
            IsConnected = pages.TotalCount > 0;
        }
        catch
        {
            IsConnected = false;
        }
    }

    private async Task LoadPagesAsync()
    {
        try
        {
            var result = await FacebookPageService.GetListAsync(new PagedAndSortedResultRequestDto { MaxResultCount = 100 });
            FacebookPages = result.Items.ToList();
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error loading pages: {ex.Message}");
            FacebookPages = new List<FacebookPageDto>();
        }
    }

    private async Task LoadPostsAsync()
    {
        if (!IsConnected)
        {
            return;
        }

        IsLoading = true;
        await InvokeAsync(StateHasChanged);

        try
        {
            var input = new GetPostsForSelectionInput
            {
                Limit = PostLimit,
                FacebookPageId = SelectedPageId
            };

            FacebookPosts = await FacebookPostService.GetPostsForSelectionAsync(input);
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error loading posts: {ex.Message}");
            FacebookPosts = new List<FacebookPostSelectionDto>();
        }
        finally
        {
            IsLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task OnPageFilterChanged(ChangeEventArgs e)
    {
        await LoadPostsAsync();
    }

    private async Task OnLimitChanged(ChangeEventArgs e)
    {
        await LoadPostsAsync();
    }

    private Task CreateCampaignAsync(FacebookPostSelectionDto post)
    {
        var queryParams = new Dictionary<string, object?>
        {
            ["postId"] = post.FacebookPostId,
            ["pageId"] = post.FacebookPageId,
            ["pageName"] = post.PageName,
            ["postContent"] = post.Message,
            ["postPermalinkUrl"] = post.PermalinkUrl,
            ["postPictureUrl"] = post.PictureUrl,
            ["postCreatedTime"] = post.FacebookCreatedTime.ToString("O")
        };

        var queryString = string.Join("&", queryParams
            .Where(kvp => kvp.Value != null)
            .Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value!.ToString()!)}"));

        NavigationManager.NavigateTo($"/campaigns/create-from-post?{queryString}");
        return Task.CompletedTask;
    }

    private async Task EditCampaignAsync(FacebookPostSelectionDto post)
    {
        try
        {
            // Find the existing campaign for this post
            var campaign = await CampaignService.GetActiveCampaignForPostAsync(post.FacebookPostId);
            if (campaign != null)
            {
                NavigationManager.NavigateTo($"/campaigns/edit/{campaign.Id}");
            }
            else
            {
                await UiMessage.Error("No active campaign found for this post.");
            }
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error finding campaign: {ex.Message}");
        }
    }

    private async Task OpenFacebookPostAsync(string permalinkUrl)
    {
        await JSRuntime.InvokeVoidAsync("open", permalinkUrl, "_blank");
    }

    private void NavigateBack()
    {
        NavigationManager.NavigateTo("/campaigns");
    }
}
