using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Identity;

namespace FbAutoReplyPlatformExpress.Entities;

public class FacebookUser : FullAuditedAggregateRoot<Guid>
{
    [Required]
    [StringLength(256)]
    public string FacebookId { get; set; } = string.Empty;

    [Required]
    public Guid UserId { get; set; }

    [StringLength(512)]
    public string AccessToken { get; set; } = string.Empty;

    [StringLength(512)]
    public string RefreshToken { get; set; } = string.Empty;

    public DateTime? TokenExpiresAt { get; set; }

    [StringLength(256)]
    public string FacebookEmail { get; set; } = string.Empty;

    [StringLength(256)]
    public string FacebookName { get; set; } = string.Empty;

    [StringLength(512)]
    public string ProfilePictureUrl { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    // Navigation property
    public virtual IdentityUser User { get; set; } = null!;

    protected FacebookUser()
    {
        // For EF Core
    }

    public FacebookUser(
        Guid id,
        string facebookId,
        Guid userId,
        string accessToken,
        string facebookEmail,
        string facebookName) : base(id)
    {
        FacebookId = facebookId;
        UserId = userId;
        AccessToken = accessToken;
        FacebookEmail = facebookEmail;
        FacebookName = facebookName;
        IsActive = true;
    }

    public void UpdateTokens(string accessToken, string? refreshToken = null, DateTime? expiresAt = null)
    {
        AccessToken = accessToken;
        if (!string.IsNullOrEmpty(refreshToken))
        {
            RefreshToken = refreshToken;
        }
        TokenExpiresAt = expiresAt;
    }

    public void UpdateProfile(string facebookName, string profilePictureUrl)
    {
        FacebookName = facebookName;
        ProfilePictureUrl = profilePictureUrl;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void Activate()
    {
        IsActive = true;
    }
}
