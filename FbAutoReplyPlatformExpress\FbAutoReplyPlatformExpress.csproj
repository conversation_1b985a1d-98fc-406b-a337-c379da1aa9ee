<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
  	<Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.0" />
    <ProjectReference Include="..\FbAutoReplyPlatformExpress.Client\FbAutoReplyPlatformExpress.Client.csproj" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.6.2" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.6.2" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Twitter" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Facebook" Version="9.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Pro.Public.Blazor.WebAssembly.Bundling" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.Application" Version="9.1.1" />

    <PackageReference Include="Volo.Abp.Account.Pro.Public.Web.OpenIddict" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Public.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Public.Application" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Public.Blazor.Server" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.Application" Version="9.1.1" />

    <PackageReference Include="Volo.Abp.OpenIddict.Pro.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.Application" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.1.1" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="Volo.Abp.LanguageManagement.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.Application" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.TextTemplateManagement.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.Application" Version="9.1.1" />
  </ItemGroup>


  <ItemGroup>
    <PackageReference Include="Volo.Abp.AuditLogging.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.Blazor.WebAssembly.Bundling" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.Application" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.FeatureManagement.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.SettingManagement.Blazor.Server" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonX" Version="4.1.1" />
    <PackageReference Include="Volo.Abp.AspNetCore.Components.Server.LeptonXTheme" Version="4.1.1" />
    <PackageReference Include="Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXTheme.Bundling" Version="4.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AuditLogging.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Studio.Client.AspNetCore" Version="0.9.26" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Commercial.SuiteTemplates" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
      <PrivateAssets>compile; contentFiles; build; buildMultitargeting; buildTransitive; analyzers; native</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup Condition="Exists('./openiddict.pfx')">
    <None Remove="openiddict.pfx" />
    <EmbeddedResource Include="openiddict.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Pages\**\*.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Pages\**\*.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Content Remove="$(UserProfile)\.nuget\packages\*\*\contentFiles\any\*\*.abppkg*" />
  </ItemGroup>

</Project>
