﻿using Blazorise.Bootstrap5;
using Blazorise.Icons.FontAwesome;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Volo.Abp.AspNetCore.Components.Web;
using FbAutoReplyPlatformExpress.Menus;
using Volo.Abp.Account;
using Volo.Abp.AspNetCore.Components.WebAssembly.LeptonXTheme;
using Volo.Abp.AspNetCore.Components.Web.LeptonXTheme;
using Volo.Abp.AspNetCore.Components.Web.LeptonXTheme.Components;
using Volo.Abp.LeptonX.Shared;
using Volo.Abp.AspNetCore.Components.Web.Theming.Routing;
using Volo.Abp.OpenIddict;
using Volo.Abp.Identity.Pro.Blazor.Server.WebAssembly;
using Volo.Abp.AuditLogging;
using Volo.Abp.AuditLogging.Blazor.WebAssembly;
using Volo.Abp.LanguageManagement;
using Volo.Abp.LanguageManagement.Blazor.WebAssembly;
using Volo.Abp.TextTemplateManagement;
using Volo.Abp.TextTemplateManagement.Blazor.WebAssembly;
using Volo.Abp.OpenIddict.Pro.Blazor.WebAssembly;
using Volo.Abp.Account.Pro.Admin.Blazor.WebAssembly;
using Volo.Abp.Autofac.WebAssembly;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.FeatureManagement.Blazor.WebAssembly;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.PermissionManagement.Blazor.WebAssembly;
using Volo.Abp.SettingManagement.Blazor.WebAssembly;
using Volo.Abp.SettingManagement;
using Volo.Abp.UI.Navigation;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Components.WebAssembly.Theming.Bundling;

namespace FbAutoReplyPlatformExpress;

[DependsOn(
    typeof(FbAutoReplyPlatformExpressContractsModule),
        
    // ABP Framework packages
    typeof(AbpAutofacWebAssemblyModule),

    // Theme
    typeof(AbpAspNetCoreComponentsWebAssemblyLeptonXThemeModule),

    // Account module packages
    typeof(AbpAccountAdminBlazorWebAssemblyModule),
    typeof(AbpAccountAdminHttpApiClientModule),

    // Identity module packages
    typeof(AbpIdentityProBlazorWebAssemblyModule),
    typeof(AbpIdentityHttpApiClientModule),

    typeof(AbpOpenIddictProBlazorWebAssemblyModule),
    typeof(AbpOpenIddictProHttpApiClientModule),

    // Audit logging module packages
    typeof(AbpAuditLoggingBlazorWebAssemblyModule),
    typeof(AbpAuditLoggingHttpApiClientModule),
    
    // Text Template Management module packages
    typeof(TextTemplateManagementBlazorWebAssemblyModule),
    typeof(TextTemplateManagementHttpApiClientModule),
        
    // Language Management module packages
    typeof(LanguageManagementBlazorWebAssemblyModule),
    typeof(LanguageManagementHttpApiClientModule),

    // Permission Management module packages
    typeof(AbpPermissionManagementBlazorWebAssemblyModule),
    typeof(AbpPermissionManagementHttpApiClientModule),

    // Feature Management module packages
    typeof(AbpFeatureManagementBlazorWebAssemblyModule),
    typeof(AbpFeatureManagementHttpApiClientModule),

    // Setting Management module packages
    typeof(AbpSettingManagementHttpApiClientModule),
    typeof(AbpSettingManagementBlazorWebAssemblyModule)
)]
public class FbAutoReplyPlatformExpressClientModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<AbpAspNetCoreComponentsWebOptions>(options =>
        {
            options.IsBlazorWebApp = true;
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var environment = context.Services.GetSingletonInstance<IWebAssemblyHostEnvironment>();
        var builder = context.Services.GetSingletonInstance<WebAssemblyHostBuilder>();

        ConfigureAuthentication(builder);
        ConfigureHttpClient(context, environment);
        ConfigureBlazorise(context);
        ConfigureRouter(context);
        ConfigureMenu(context);
        ConfigureAutoMapper(context);
        ConfigureTheme();

        context.Services.AddHttpClientProxies(typeof(FbAutoReplyPlatformExpressContractsModule).Assembly);
    }
    
    private void ConfigureTheme()
    {
        Configure<LeptonXThemeOptions>(options =>
        {
            options.DefaultStyle = LeptonXStyleNames.Dim;
        });

        Configure<LeptonXThemeBlazorOptions>(options =>
        {
            // When Layout is changed, the `options.Parameters["LeptonXTheme.Layout"]` in FbAutoReplyPlatformExpressModule.cs should be updated accordingly.
            options.Layout = LeptonXBlazorLayouts.SideMenu;
        });
    }

    private void ConfigureRouter(ServiceConfigurationContext context)
    {
        Configure<AbpRouterOptions>(options =>
        {
            options.AppAssembly = typeof(FbAutoReplyPlatformExpressClientModule).Assembly;
        });
    }

    private void ConfigureMenu(ServiceConfigurationContext context)
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new FbAutoReplyPlatformExpressMenuContributor(context.Services.GetConfiguration()));
        });
    }

    private void ConfigureBlazorise(ServiceConfigurationContext context)
    {
        context.Services
            .AddBootstrap5Providers()
            .AddFontAwesomeIcons();
    }

    private static void ConfigureAuthentication(WebAssemblyHostBuilder builder)
    {
        builder.Services.AddBlazorWebAppServices();
    }

    private static void ConfigureHttpClient(ServiceConfigurationContext context, IWebAssemblyHostEnvironment environment)
    {
        context.Services.AddTransient(sp => new HttpClient
        {
            BaseAddress = new Uri(environment.BaseAddress)
        });
    }

    private void ConfigureAutoMapper(ServiceConfigurationContext context)
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<FbAutoReplyPlatformExpressClientModule>();
        });
    }
}
