@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Blazorise

<div class="personalization-tags mb-2">
    <small class="text-muted me-2">Personalization Tags:</small>
    <Button Color="Color.Light" Size="Size.Small" Class="me-1" @onclick="() => InsertTag(FirstNameTag)">
        <Icon Name="IconName.User" Class="me-1" />
        First Name
    </Button>
    <Button Color="Color.Light" Size="Size.Small" @onclick="() => InsertTag(LastNameTag)">
        <Icon Name="IconName.User" Class="me-1" />
        Last Name
    </Button>
</div>

@code {
    [Parameter]
    public EventCallback<string> OnTagSelected { get; set; }

    private const string FirstNameTag = "#FIRST_NAME#";
    private const string LastNameTag = "#LAST_NAME#";

    private async Task InsertTag(string tag)
    {
        await OnTagSelected.InvokeAsync(tag);
    }
}
