@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components.Web

@if (PreviewData != null)
{
    <Card class="post-preview-card">
        <CardHeader class="post-preview-header">
            <div class="d-flex align-items-center">
                <img src="@(PreviewData.PageProfilePictureUrl ?? "/images/default-page.png")"
                     alt="@PreviewData.PageName"
                     class="page-avatar me-3" />
                <div>
                    <h6 class="mb-0">@PreviewData.PageName</h6>
                    <small class="text-muted">
                        <Icon Name="IconName.Globe" class="me-1" />
                        @GetPostTimeText()
                    </small>
                </div>
            </div>
        </CardHeader>
    
    <CardBody class="post-preview-body">
        <!-- Post Message -->
        @if (!string.IsNullOrEmpty(PreviewData.Message))
        {
            <div class="post-message mb-3">
                @((MarkupString)FormatPostMessage(PreviewData.Message))
            </div>
        }

        <!-- Media Content -->
        @if (PreviewData.MediaUrls?.Any() == true)
        {
            <div class="post-media mb-3">
                @if (PreviewData.PostType == FacebookPostType.Image)
                {
                    @if (PreviewData.MediaUrls.Count == 1)
                    {
                        <!-- Single Image -->
                        <img src="@PreviewData.MediaUrls.First()" 
                             alt="Post image" 
                             class="single-image" />
                    }
                    else
                    {
                        <!-- Multiple Images Grid -->
                        <div class="images-grid @GetGridClass(PreviewData.MediaUrls.Count)">
                            @foreach (var (imageUrl, index) in PreviewData.MediaUrls.Take(4).Select((url, i) => (url, i)))
                            {
                                <div class="grid-item @(index == 3 && PreviewData.MediaUrls.Count > 4 ? "more-overlay" : "")">
                                    <img src="@imageUrl" alt="Post image @(index + 1)" />
                                    @if (index == 3 && PreviewData.MediaUrls.Count > 4)
                                    {
                                        <div class="more-count">
                                            +@(PreviewData.MediaUrls.Count - 4)
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    }
                }
                else if (PreviewData.PostType == FacebookPostType.Video)
                {
                    <!-- Video -->
                    <video controls class="post-video">
                        <source src="@PreviewData.MediaUrls.First()" />
                        Your browser does not support the video tag.
                    </video>
                }
            </div>
        }

        <!-- Link Preview -->
        @if (!string.IsNullOrEmpty(PreviewData.LinkUrl))
        {
            <div class="link-preview">
                @if (!string.IsNullOrEmpty(PreviewData.LinkImageUrl))
                {
                    <img src="@PreviewData.LinkImageUrl" alt="Link preview" class="link-image" />
                }
                <div class="link-content">
                    <div class="link-domain">@GetDomainFromUrl(PreviewData.LinkUrl)</div>
                    @if (!string.IsNullOrEmpty(PreviewData.LinkTitle))
                    {
                        <div class="link-title">@PreviewData.LinkTitle</div>
                    }
                    @if (!string.IsNullOrEmpty(PreviewData.LinkDescription))
                    {
                        <div class="link-description">@PreviewData.LinkDescription</div>
                    }
                </div>
            </div>
        }
    </CardBody>

    <!-- Post Actions (Like, Comment, Share) -->
    <CardFooter class="post-actions">
        <div class="d-flex justify-content-around">
            <Button Color="Color.Light" class="action-btn">
                <Icon Name="IconName.ThumbsUp" class="me-2" />
                Like
            </Button>
            <Button Color="Color.Light" class="action-btn">
                <Icon Name="IconName.Comment" class="me-2" />
                Comment
            </Button>
            <Button Color="Color.Light" class="action-btn">
                <Icon Name="IconName.Share" class="me-2" />
                Share
            </Button>
        </div>
    </CardFooter>
</Card>

@code {
    [Parameter] public FacebookPostPreviewDto PreviewData { get; set; } = new();
    [Parameter] public bool IsScheduled { get; set; }
    [Parameter] public DateTime? ScheduledTime { get; set; }

    private string GetPostTimeText()
    {
        if (IsScheduled && ScheduledTime.HasValue)
        {
            return $"Scheduled for {ScheduledTime.Value:MMM d, yyyy 'at' h:mm tt}";
        }
        return "Just now";
    }

    private string FormatPostMessage(string message)
    {
        if (string.IsNullOrEmpty(message))
            return string.Empty;

        // Simple formatting - convert line breaks to <br> tags
        return message.Replace("\n", "<br>");
    }

    private string GetGridClass(int imageCount)
    {
        return imageCount switch
        {
            2 => "grid-2",
            3 => "grid-3",
            >= 4 => "grid-4",
            _ => "grid-1"
        };
    }

    private string GetDomainFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host.Replace("www.", "").ToUpper();
        }
        catch
        {
            return "LINK";
        }
    }
}

<style>
    .post-preview-card {
        max-width: 500px;
        margin: 0 auto;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .post-preview-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .page-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .post-message {
        font-size: 1rem;
        line-height: 1.4;
        white-space: pre-wrap;
    }

    .post-media {
        border-radius: 8px;
        overflow: hidden;
    }

    .single-image {
        width: 100%;
        max-height: 400px;
        object-fit: cover;
    }

    .images-grid {
        display: grid;
        gap: 2px;
        border-radius: 8px;
        overflow: hidden;
    }

    .grid-2 {
        grid-template-columns: 1fr 1fr;
    }

    .grid-3 {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
    }

    .grid-3 .grid-item:first-child {
        grid-row: 1 / 3;
    }

    .grid-4 {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
    }

    .grid-item {
        position: relative;
        aspect-ratio: 1;
        overflow: hidden;
    }

    .grid-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .more-overlay::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
    }

    .more-count {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 1.5rem;
        font-weight: bold;
        z-index: 1;
    }

    .post-video {
        width: 100%;
        max-height: 400px;
        border-radius: 8px;
    }

    .link-preview {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
    }

    .link-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .link-content {
        padding: 12px;
    }

    .link-domain {
        font-size: 0.75rem;
        color: #6c757d;
        text-transform: uppercase;
        margin-bottom: 4px;
    }

    .link-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: #1877f2;
    }

    .link-description {
        font-size: 0.875rem;
        color: #65676b;
        line-height: 1.3;
    }

    .post-actions {
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        padding: 8px 16px;
    }

    .action-btn {
        flex: 1;
        border: none;
        background: transparent;
        color: #65676b;
        font-weight: 600;
        padding: 8px 12px;
        border-radius: 6px;
        transition: background-color 0.2s;
    }

    .action-btn:hover {
        background-color: #e4e6ea;
    }
</style>
}
else
{
    <Card class="post-preview-card">
        <CardBody class="text-center text-muted py-5">
            <Icon Name="IconName.Eye" Size="IconSize.x2" class="mb-3" />
            <p class="mb-0">Select a Facebook page and enter content to see preview</p>
        </CardBody>
    </Card>
}
