using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Volo.Abp.DependencyInjection;
using FbAutoReplyPlatformExpress.Services.Dtos;

namespace FbAutoReplyPlatformExpress.Services;

public interface IPersonalizationService
{
    Task<string> PersonalizeMessageAsync(string message, string commenterFacebookId, string commenterName, string pageAccessToken);
    string PersonalizeMessage(string message, string firstName, string lastName);
    Task<CardReplyData> PersonalizeCardReplyAsync(CardReplyData cardReplyData, string commenterFacebookId, string commenterName, string pageAccessToken);
    CardReplyData PersonalizeCardReply(CardReplyData cardReplyData, string firstName, string lastName);
    Task<string> GeneratePublicReplyAsync(PublicReplyType replyType, string customMessage, string commenterFacebookId, string commenterName, string pageAccessToken);
}

public class PersonalizationService : IPersonalizationService, ITransientDependency
{
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly ILogger<PersonalizationService> _logger;
    private readonly IPublicReplyGeneratorService _publicReplyGeneratorService;

    public PersonalizationService(
        FacebookGraphApiService facebookGraphApiService,
        ILogger<PersonalizationService> logger,
        IPublicReplyGeneratorService publicReplyGeneratorService)
    {
        _facebookGraphApiService = facebookGraphApiService;
        _logger = logger;
        _publicReplyGeneratorService = publicReplyGeneratorService;
    }

    public async Task<string> PersonalizeMessageAsync(string message, string commenterFacebookId, string commenterName, string pageAccessToken)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }

        // Check if message contains personalization tags
        if (!message.Contains("#FIRST_NAME#") && !message.Contains("#LAST_NAME#"))
        {
            return message;
        }

        try
        {
            // First try to get detailed user info from Facebook Graph API
            
            if (!string.IsNullOrEmpty(commenterName))
            {
                var (firstName, lastName) = ExtractFirstAndLastName(commenterName);
                return PersonalizeMessage(message, firstName, lastName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to fetch detailed user info for commenter {CommenterFacebookId}, falling back to webhook name", commenterFacebookId);
        }


        // If no name available, return message with tags removed or replaced with fallback
        _logger.LogWarning("No commenter name available for personalization, using fallback for commenter {CommenterFacebookId}", commenterFacebookId);
        return PersonalizeMessage(message, "Friend", "");
    }

    public string PersonalizeMessage(string message, string firstName, string lastName)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }

        var personalizedMessage = message;

        // Replace first name tag
        if (!string.IsNullOrEmpty(firstName))
        {
            personalizedMessage = personalizedMessage.Replace("#FIRST_NAME#", firstName);
        }
        else
        {
            personalizedMessage = personalizedMessage.Replace("#FIRST_NAME#", "Friend");
        }

        // Replace last name tag
        if (!string.IsNullOrEmpty(lastName))
        {
            personalizedMessage = personalizedMessage.Replace("#LAST_NAME#", lastName);
        }
        else
        {
            personalizedMessage = personalizedMessage.Replace("#LAST_NAME#", "");
        }

        return personalizedMessage.Trim();
    }

    public async Task<CardReplyData> PersonalizeCardReplyAsync(CardReplyData cardReplyData, string commenterFacebookId, string commenterName, string pageAccessToken)
    {
        if (cardReplyData == null)
        {
            return cardReplyData;
        }

        try
        {
            if (!string.IsNullOrEmpty(commenterName))
            {
                var (firstName, lastName) = ExtractFirstAndLastName(commenterName);
                return PersonalizeCardReply(cardReplyData, firstName, lastName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to fetch detailed user info for card reply personalization for commenter {CommenterFacebookId}, falling back to webhook name", commenterFacebookId);
        }


        // If no name available, return card with tags replaced with fallback
        _logger.LogWarning("No commenter name available for card reply personalization, using fallback for commenter {CommenterFacebookId}", commenterFacebookId);
        return PersonalizeCardReply(cardReplyData, "Friend", "");
    }

    public CardReplyData PersonalizeCardReply(CardReplyData cardReplyData, string firstName, string lastName)
    {
        if (cardReplyData == null)
        {
            return cardReplyData;
        }

        // Create a deep copy to avoid modifying the original
        var personalizedCard = new CardReplyData
        {
            Title = PersonalizeMessage(cardReplyData.Title, firstName, lastName),
            ImageUrl = cardReplyData.ImageUrl,
            Subtitle = !string.IsNullOrEmpty(cardReplyData.Subtitle)
                ? PersonalizeMessage(cardReplyData.Subtitle, firstName, lastName)
                : cardReplyData.Subtitle,
            Buttons = cardReplyData.Buttons.Select(button => new CardReplyButton
            {
                Title = PersonalizeMessage(button.Title, firstName, lastName),
                Type = button.Type,
                Url = button.Url,
                PhoneNumber = button.PhoneNumber
            }).ToList()
        };

        return personalizedCard;
    }

    public async Task<string> GeneratePublicReplyAsync(PublicReplyType replyType, string customMessage, string commenterFacebookId, string commenterName, string pageAccessToken)
    {
        switch (replyType)
        {
            case PublicReplyType.Emoji:
                return _publicReplyGeneratorService.GenerateEmojiReply();

            case PublicReplyType.Welcome:
                try
                {
                    // Extract first name for welcome message
                    var (firstName, lastName) = ExtractFirstAndLastName(commenterName ?? "Friend");
                    return await _publicReplyGeneratorService.GenerateWelcomeReplyAsync(firstName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to generate welcome reply for commenter {CommenterFacebookId}, using fallback", commenterFacebookId);
                    return await _publicReplyGeneratorService.GenerateWelcomeReplyAsync("Friend");
                }

            case PublicReplyType.Custom:
            default:
                // For custom replies, personalize the provided message
                return await PersonalizeMessageAsync(customMessage, commenterFacebookId, commenterName, pageAccessToken);
        }
    }


    private (string firstName, string lastName) ExtractFirstAndLastName(string fullName)
    {
        if (string.IsNullOrWhiteSpace(fullName))
        {
            return ("Friend", "");
        }

        var nameParts = fullName.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);
        
        if (nameParts.Length == 0)
        {
            return ("Friend", "");
        }
        else if (nameParts.Length == 1)
        {
            return (nameParts[0], "");
        }
        else
        {
            var firstName = nameParts[0];
            var lastName = string.Join(" ", nameParts[1..]);
            return (firstName, lastName);
        }
    }
}

public class FacebookCommenterInfo
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("first_name")]
    public string FirstName { get; set; } = string.Empty;

    [JsonProperty("last_name")]
    public string LastName { get; set; } = string.Empty;
}
