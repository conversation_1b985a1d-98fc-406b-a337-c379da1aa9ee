using System;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace FbAutoReplyPlatformExpress.Services;

public interface IAutoReplyCampaignService : IApplicationService
{
    Task<PagedResultDto<AutoReplyCampaignDto>> GetListAsync(GetCampaignsInput input);
    Task<AutoReplyCampaignDto> GetAsync(Guid id);
    Task<AutoReplyCampaignDto> CreateAsync(CreateAutoReplyCampaignDto input);
    Task<AutoReplyCampaignDto> CreateFromPostAsync(CreateCampaignFromPostDto input);
    Task<AutoReplyCampaignDto> UpdateAsync(Guid id, UpdateAutoReplyCampaignDto input);
    Task DeleteAsync(Guid id);
    
    Task<AutoReplyCampaignDto> ActivateAsync(Guid id);
    Task<AutoReplyCampaignDto> DeactivateAsync(Guid id);
    
    Task<PagedResultDto<CampaignActivityDto>> GetActivitiesAsync(GetActivitiesInput input);
    Task<CampaignActivityDto> GetActivityAsync(Guid id);
    
    Task<AutoReplyCampaignDto?> GetActiveCampaignForPostAsync(string facebookPostId);
}
