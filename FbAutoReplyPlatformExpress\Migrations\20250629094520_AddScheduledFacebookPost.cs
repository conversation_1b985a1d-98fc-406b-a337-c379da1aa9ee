﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FbAutoReplyPlatformExpress.Migrations
{
    /// <inheritdoc />
    public partial class AddScheduledFacebookPost : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppScheduledFacebookPosts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FacebookPageId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FacebookPostId = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    PostType = table.Column<int>(type: "int", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(2048)", maxLength: 2048, nullable: true),
                    MediaUrlsJson = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
                    LinkUrl = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    ScheduledPublishTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    PublishedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MediaUrls = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppScheduledFacebookPosts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppScheduledFacebookPosts_AppFacebookPages_FacebookPageId",
                        column: x => x.FacebookPageId,
                        principalTable: "AppFacebookPages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppScheduledFacebookPosts_FacebookPageId",
                table: "AppScheduledFacebookPosts",
                column: "FacebookPageId");

            migrationBuilder.CreateIndex(
                name: "IX_AppScheduledFacebookPosts_ScheduledPublishTime",
                table: "AppScheduledFacebookPosts",
                column: "ScheduledPublishTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppScheduledFacebookPosts_Status",
                table: "AppScheduledFacebookPosts",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppScheduledFacebookPosts");
        }
    }
}
