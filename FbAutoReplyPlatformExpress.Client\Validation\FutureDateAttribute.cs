using System.ComponentModel.DataAnnotations;

namespace FbAutoReplyPlatformExpress.Client.Validation;

public class FutureDateAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value == null)
        {
            // Null values are considered valid (use [Required] for mandatory fields)
            return true;
        }

        if (value is DateTime dateTime)
        {
            return dateTime > DateTime.Now;
        }

        return false;
    }

    public override string FormatErrorMessage(string name)
    {
        return ErrorMessage ?? $"The {name} field must be a future date and time.";
    }
}
