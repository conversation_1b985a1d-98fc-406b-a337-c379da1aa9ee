using System;
using System.Linq;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Services;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Linq;
using Volo.Abp.Uow;
using Volo.Abp.Guids;

namespace FbAutoReplyPlatformExpress.BackgroundJobs;

public class ProcessAutoReplyJob : AsyncBackgroundJob<ProcessAutoReplyJobArgs>, ITransientDependency
{
    private readonly IRepository<AutoReplyCampaign, Guid> _campaignRepository;
    private readonly IRepository<FacebookPage, Guid> _pageRepository;
    private readonly IRepository<FacebookPost, Guid> _postRepository;
    private readonly IRepository<CampaignActivity, Guid> _activityRepository;
    private readonly FacebookGraphApiService _facebookGraphApiService;
    private readonly IPersonalizationService _personalizationService;
    private readonly IAsyncQueryableExecuter _asyncExecuter;
    private readonly ILogger<ProcessAutoReplyJob> _logger;
    private readonly IGuidGenerator _guidGenerator;

    public ProcessAutoReplyJob(
        IRepository<AutoReplyCampaign, Guid> campaignRepository,
        IRepository<FacebookPage, Guid> pageRepository,
        IRepository<FacebookPost, Guid> postRepository,
        IRepository<CampaignActivity, Guid> activityRepository,
        FacebookGraphApiService facebookGraphApiService,
        IPersonalizationService personalizationService,
        IAsyncQueryableExecuter asyncExecuter,
        ILogger<ProcessAutoReplyJob> logger,
        IGuidGenerator guidGenerator)
    {
        _campaignRepository = campaignRepository;
        _pageRepository = pageRepository;
        _postRepository = postRepository;
        _activityRepository = activityRepository;
        _facebookGraphApiService = facebookGraphApiService;
        _personalizationService = personalizationService;
        _asyncExecuter = asyncExecuter;
        _logger = logger;
        _guidGenerator = guidGenerator;
    }

    [UnitOfWork]
    public override async Task ExecuteAsync(ProcessAutoReplyJobArgs args)
    {
        try
        {
            _logger.LogInformation("Processing auto-reply for campaign {CampaignId}, comment {CommentId}",
                args.CampaignId, args.CommentId);

            // Check if we've already processed this comment
            var existingActivity = await _activityRepository.FirstOrDefaultAsync(
                a => a.FacebookCommentId == args.CommentId);

            if (existingActivity != null)
            {
                _logger.LogInformation("Comment {CommentId} already processed", args.CommentId);
                return;
            }

            // Get campaign details
            var campaign = await _campaignRepository.GetAsync(args.CampaignId);
            if (!campaign.IsValidForReply())
            {
                _logger.LogWarning("Campaign {CampaignId} is not valid for replies", args.CampaignId);
                return;
            }

            // Check if user has exceeded max replies
            var userActivityCount = await _activityRepository.CountAsync(
                a => a.CampaignId == args.CampaignId &&
                     a.CommenterFacebookId == args.CommenterFacebookId);

            if (campaign.HasUserExceededReplyLimit(userActivityCount))
            {
                _logger.LogInformation("User {UserId} has exceeded max replies ({MaxReplies}) for campaign {CampaignId}",
                    args.CommenterFacebookId, campaign.MaxRepliesPerUser, args.CampaignId);
                return;
            }

            // Get page information (different approach for in-memory vs database posts)
            FacebookPage page;
            if (campaign.FacebookPostId.HasValue)
            {
                // Database post - get page through post relationship
                var post = await _postRepository.GetAsync(campaign.FacebookPostId.Value);
                page = await _pageRepository.GetAsync(post.FacebookPageId);
            }
            else
            {
                // In-memory post - get page by FacebookPageIdString
                var pageQuery = await _pageRepository.GetQueryableAsync();
                page = await _asyncExecuter.FirstOrDefaultAsync(
                    pageQuery.Where(p => p.FacebookPageId == campaign.FacebookPageIdString));

                if (page == null)
                {
                    _logger.LogError("Page {PageId} not found for in-memory campaign {CampaignId}",
                        campaign.FacebookPageIdString, args.CampaignId);
                    return;
                }
            }

            // Create activity record
            var activity = new CampaignActivity(
                _guidGenerator.Create(),
                args.CampaignId,
                args.CommentId,
                args.CommenterFacebookId,
                args.CommenterName,
                args.OriginalComment,
                args.CommentCreatedAt);

            await _activityRepository.InsertAsync(activity);

            // Send public reply if configured
            if (campaign.SendPublicReply)
            {
                try
                {
                    // Generate the appropriate public reply based on type
                    var publicReplyMessage = await _personalizationService.GeneratePublicReplyAsync(
                        campaign.PublicReplyType,
                        campaign.PublicReplyMessage,
                        args.CommenterFacebookId,
                        args.CommenterName,
                        page.PageAccessToken);

                    var publicReplyId = await _facebookGraphApiService.PostCommentReplyAsync(
                        args.CommentId, publicReplyMessage, page.PageAccessToken);

                    activity.SetPublicReply(publicReplyMessage, publicReplyId);
                    campaign.IncrementReplyCount(true);

                    _logger.LogInformation("Posted {ReplyType} public reply {ReplyId} for comment {CommentId}",
                        campaign.PublicReplyType, publicReplyId, args.CommentId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to post {ReplyType} public reply for comment {CommentId}",
                        campaign.PublicReplyType, args.CommentId);
                    activity.SetError($"Failed to post {campaign.PublicReplyType.ToString().ToLower()} public reply: {ex.Message}");
                }
            }

            // Send private message if configured
            if (campaign.SendPrivateReply && campaign.IsPrivateReplyConfigurationValid())
            {
                try
                {
                    string messageId;
                    string replyContent;

                    if (campaign.PrivateReplyType == Services.Dtos.PrivateReplyType.CardReply)
                    {
                        // Send card reply
                        var cardReplyData = campaign.GetCardReplyData();
                        if (cardReplyData != null && cardReplyData.IsValid())
                        {
                            // Personalize the card reply data
                            var personalizedCardData = await _personalizationService.PersonalizeCardReplyAsync(
                                cardReplyData,
                                args.CommenterFacebookId,
                                args.CommenterName,
                                page.PageAccessToken);

                            messageId = await _facebookGraphApiService.SendPrivateCardReplyAsync(
                                args.CommentId,
                                personalizedCardData,
                                campaign.PostPermalinkUrl ?? "",
                                page.PageAccessToken);
                            replyContent = $"Card: {personalizedCardData.Title}";
                        }
                        else
                        {
                            throw new InvalidOperationException("Invalid card reply data");
                        }
                    }
                    else
                    {
                        // Send text reply
                        if (string.IsNullOrEmpty(campaign.PrivateReplyMessage))
                        {
                            throw new InvalidOperationException("Private reply message is required for text reply");
                        }

                        // Personalize the private reply message
                        var personalizedPrivateMessage = await _personalizationService.PersonalizeMessageAsync(
                            campaign.PrivateReplyMessage,
                            args.CommenterFacebookId,
                            args.CommenterName,
                            page.PageAccessToken);

                        if (campaign.IncludePostLinkInPrivateReply && !string.IsNullOrEmpty(campaign.PostPermalinkUrl))
                        {
                            messageId = await _facebookGraphApiService.SendPrivateMessageWithTemplateAsync(
                                args.CommentId,
                                personalizedPrivateMessage,
                                campaign.PostPictureUrl,
                                campaign.PostPermalinkUrl!,
                                page.PageAccessToken);
                        }
                        else
                        {
                            messageId = await _facebookGraphApiService.SendPrivateMessageAsync(
                                args.CommentId, personalizedPrivateMessage, page.PageAccessToken);
                        }
                        replyContent = personalizedPrivateMessage;
                    }

                    activity.SetPrivateReply(replyContent, messageId);
                    campaign.IncrementReplyCount(false);

                    _logger.LogInformation("Sent private {ReplyType} {MessageId} to user {UserId}",
                        campaign.PrivateReplyType, messageId, args.CommenterFacebookId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send private {ReplyType} for comment {CommentId}",
                        campaign.PrivateReplyType, args.CommentId);
                    activity.SetError($"Failed to send private {campaign.PrivateReplyType.ToString().ToLower()}: {ex.Message}");
                }
            }

            // Like comment if configured
            if (campaign.SendLike)
            {
                try
                {
                    await _facebookGraphApiService.LikeCommentAsync(args.CommentId, page.PageAccessToken);

                    activity.SetLike();
                    campaign.IncrementLikeCount();

                    _logger.LogInformation("Liked comment {CommentId} for user {UserId}",
                        args.CommentId, args.CommenterFacebookId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to like comment {CommentId}", args.CommentId);
                    activity.SetError($"Failed to like comment: {ex.Message}");
                }
            }

            // Update entities
            await _activityRepository.UpdateAsync(activity);
            await _campaignRepository.UpdateAsync(campaign);

            _logger.LogInformation("Successfully processed auto-reply for comment {CommentId}", args.CommentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing auto-reply job for comment {CommentId}", args.CommentId);
            throw;
        }
    }
}
