﻿using Volo.Abp.Ui.Branding;
using Volo.Abp.DependencyInjection;
using Microsoft.Extensions.Localization;
using FbAutoReplyPlatformExpress.Localization;

namespace FbAutoReplyPlatformExpress;

public class FbAutoReplyPlatformExpressBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<FbAutoReplyPlatformExpressResource> _localizer;

    public FbAutoReplyPlatformExpressBrandingProvider(IStringLocalizer<FbAutoReplyPlatformExpressResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}
