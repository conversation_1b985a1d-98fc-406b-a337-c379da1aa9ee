namespace FbAutoReplyPlatformExpress.Permissions;

public static class FbAutoReplyPlatformExpressPermissions
{
    public const string GroupName = "FbAutoReplyPlatformExpress";

    public static class Dashboard
    {
        public const string DashboardGroup = GroupName + ".Dashboard";
        public const string Host = DashboardGroup + ".Host";
    }

    public static class Facebook
    {
        public const string Default = GroupName + ".Facebook";
        public const string Connect = Default + ".Connect";
        public const string Disconnect = Default + ".Disconnect";
        public const string ManagePages = Default + ".ManagePages";
        public const string ViewPages = Default + ".ViewPages";
    }

    public static class Posts
    {
        public const string Default = GroupName + ".Posts";
        public const string View = Default + ".View";
        public const string Sync = Default + ".Sync";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string Publish = Default + ".Publish";
        public const string Schedule = Default + ".Schedule";
    }

    public static class Campaigns
    {
        public const string Default = GroupName + ".Campaigns";
        public const string Create = Default + ".Create";
        public const string Edit = Default + ".Edit";
        public const string Delete = Default + ".Delete";
        public const string View = Default + ".View";
        public const string Activate = Default + ".Activate";
        public const string Deactivate = Default + ".Deactivate";
        public const string ViewActivities = Default + ".ViewActivities";
    }

    //Add your own permission names. Example:
    //public const string MyPermission1 = GroupName + ".MyPermission1";
}
