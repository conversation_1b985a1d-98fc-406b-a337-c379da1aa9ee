﻿using Volo.Abp.DependencyInjection;
using Microsoft.EntityFrameworkCore;

namespace FbAutoReplyPlatformExpress.Data;

public class FbAutoReplyPlatformExpressDbSchemaMigrator : ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public FbAutoReplyPlatformExpressDbSchemaMigrator(
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        
        /* We intentionally resolving the FbAutoReplyPlatformExpressDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<FbAutoReplyPlatformExpressDbContext>()
            .Database
            .MigrateAsync();

    }
}
