﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FbAutoReplyPlatformExpress.Migrations
{
    /// <inheritdoc />
    public partial class AddCardReplySupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add CardReplyDataJson column
            migrationBuilder.AddColumn<string>(
                name: "CardReplyData<PERSON><PERSON>",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(4000)",
                maxLength: 4000,
                nullable: true);

            // Add PrivateReplyType column only if it doesn't exist
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[AppAutoReplyCampaigns]') AND name = 'PrivateReplyType')
                BEGIN
                    ALTER TABLE [AppAutoReplyCampaigns] ADD [PrivateReplyType] int NOT NULL DEFAULT 0;
                END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>Reply<PERSON><PERSON><PERSON><PERSON>",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "PrivateReplyType",
                table: "AppAutoReplyCampaigns");
        }
    }
}
