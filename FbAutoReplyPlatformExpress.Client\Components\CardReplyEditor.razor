@using FbAutoReplyPlatformExpress.Services.Dtos
@using Blazorise
@using Blazorise.Components

<Card>
    <CardHeader>
        <h6 class="mb-0">Card Reply Configuration</h6>
    </CardHeader>
    <CardBody>
        <Validations @ref="validationsRef" Mode="ValidationMode.Manual" ValidateOnLoad="false">
            <!-- Title Field (Required) -->
            <Validation>
                <Field>
                    <FieldLabel>Title *</FieldLabel>
                    <TextEdit Text="@CardData.Title"
                              TextChanged="@OnTitleChanged"
                              Placeholder="Enter card title (max 80 characters)"
                              MaxLength="80">
                        <Feedback>
                            <ValidationError />
                        </Feedback>
                    </TextEdit>
                    <FieldHelp>The main title of your card reply (required)</FieldHelp>
                </Field>
            </Validation>

            <!-- Image Field (Optional) -->
            <Field>
                <FieldLabel>Image (Optional)</FieldLabel>
                <Row>
                    <Column ColumnSize="ColumnSize.Is8">
                        <TextEdit Text="@CardData.ImageUrl"
                                  TextChanged="@OnImageUrlChanged"
                                  Placeholder="Enter image URL" />
                    </Column>
                    <Column ColumnSize="ColumnSize.Is4">
                        <Button Color="Color.Secondary" Size="Size.Small" Disabled="true">
                            <Icon Name="IconName.FileUpload" /> Upload
                        </Button>
                    </Column>
                </Row>
                <FieldHelp>Provide an image URL or upload an image (upload coming soon)</FieldHelp>
            </Field>

            <!-- Subtitle Field (Optional) -->
            <Field>
                <FieldLabel>Subtitle (Optional)</FieldLabel>
                <TextEdit Text="@CardData.Subtitle"
                          TextChanged="@OnSubtitleChanged"
                          Placeholder="Enter subtitle (max 80 characters)"
                          MaxLength="80" />
                <FieldHelp>Additional description text for your card</FieldHelp>
            </Field>

            <!-- Buttons Section -->
            <Field>
                <FieldLabel>Buttons (Optional - Max 3)</FieldLabel>
                @for (int i = 0; i < CardData.Buttons.Count; i++)
                {
                    var buttonIndex = i; // Capture for closure
                    var button = CardData.Buttons[buttonIndex];
                    
                    <Card Class="mb-2 border">
                        <CardBody Class="p-3">
                            <Row>
                                <Column ColumnSize="ColumnSize.Is11">
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>Button Title *</FieldLabel>
                                                <TextEdit Text="@button.Title"
                                                          TextChanged="@(async (string value) => await OnButtonFieldChanged(buttonIndex, nameof(button.Title), value))"
                                                          Placeholder="Button text"
                                                          MaxLength="20" />
                                            </Field>
                                        </Column>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>Button Type</FieldLabel>
                                                <Select SelectedValue="@button.Type"
                                                        SelectedValueChanged="@(async (CardReplyButtonType value) => await OnButtonTypeChanged(buttonIndex, value))"
                                                        TValue="CardReplyButtonType">
                                                    <SelectItem Value="CardReplyButtonType.WebUrl">Web URL</SelectItem>
                                                    <SelectItem Value="CardReplyButtonType.PhoneNumber">Phone Number</SelectItem>
                                                </Select>
                                            </Field>
                                        </Column>
                                    </Row>
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is12">
                                            @if (button.Type == CardReplyButtonType.WebUrl)
                                            {
                                                <Field>
                                                    <FieldLabel>URL *</FieldLabel>
                                                    <TextEdit Text="@button.Url"
                                                              TextChanged="@(async (string value) => await OnButtonFieldChanged(buttonIndex, nameof(button.Url), value))"
                                                              Placeholder="https://example.com" />
                                                </Field>
                                            }
                                            else
                                            {
                                                <Field>
                                                    <FieldLabel>Phone Number *</FieldLabel>
                                                    <TextEdit Text="@button.PhoneNumber"
                                                              TextChanged="@(async (string value) => await OnButtonFieldChanged(buttonIndex, nameof(button.PhoneNumber), value))"
                                                              Placeholder="+1234567890"
                                                              MaxLength="20" />
                                                </Field>
                                            }
                                        </Column>
                                    </Row>
                                </Column>
                                <Column ColumnSize="ColumnSize.Is1" Class="d-flex align-items-center">
                                    <Button Color="Color.Danger" Size="Size.Small" Clicked="@(() => RemoveButton(buttonIndex))">
                                        <Icon Name="IconName.Delete" />
                                    </Button>
                                </Column>
                            </Row>
                        </CardBody>
                    </Card>
                }

                @if (CardData.Buttons.Count < 3)
                {
                    <Button Color="Color.Primary" Size="Size.Small" Clicked="@AddButton" Class="mt-2">
                        <Icon Name="IconName.Add" /> Add Button
                    </Button>
                }

                <FieldHelp>Add up to 3 buttons for user interaction</FieldHelp>
            </Field>

            <!-- Validation Summary -->
            @if (!IsValid)
            {
                <Alert Color="Color.Warning" Visible="true" Class="mt-3">
                    <Icon Name="IconName.ExclamationTriangle" />
                    <strong>Validation Error:</strong> @ValidationMessage
                </Alert>
            }
        </Validations>
    </CardBody>
</Card>

@code {
    [Parameter] public CardReplyData CardData { get; set; } = new();
    [Parameter] public EventCallback<CardReplyData> CardDataChanged { get; set; }
    [Parameter] public EventCallback<bool> OnValidationChanged { get; set; }

    private Validations? validationsRef;
    private bool IsValid => CardData.IsValid();
    private string ValidationMessage => GetValidationMessage();

    protected override async Task OnInitializedAsync()
    {
        await NotifyValidationChanged();
    }

    private async Task AddButton()
    {
        if (CardData.Buttons.Count < 3)
        {
            CardData.Buttons.Add(new CardReplyButton());
            await NotifyDataChanged();
            await NotifyValidationChanged();
        }
    }

    private async Task RemoveButton(int index)
    {
        if (index >= 0 && index < CardData.Buttons.Count)
        {
            CardData.Buttons.RemoveAt(index);
            await NotifyDataChanged();
            await NotifyValidationChanged();
        }
    }

    private async Task NotifyDataChanged()
    {
        await CardDataChanged.InvokeAsync(CardData);
    }

    private async Task NotifyValidationChanged()
    {
        await OnValidationChanged.InvokeAsync(IsValid);
    }

    private async Task OnTitleChanged(string value)
    {
        CardData.Title = value;
        await NotifyDataChanged();
        await NotifyValidationChanged();
    }

    private async Task OnImageUrlChanged(string value)
    {
        CardData.ImageUrl = value;
        await NotifyDataChanged();
        await NotifyValidationChanged();
    }

    private async Task OnSubtitleChanged(string value)
    {
        CardData.Subtitle = value;
        await NotifyDataChanged();
        await NotifyValidationChanged();
    }

    private async Task OnButtonFieldChanged(int buttonIndex, string fieldName, string value)
    {
        if (buttonIndex >= 0 && buttonIndex < CardData.Buttons.Count)
        {
            var button = CardData.Buttons[buttonIndex];
            switch (fieldName)
            {
                case nameof(CardReplyButton.Title):
                    button.Title = value;
                    break;
                case nameof(CardReplyButton.Url):
                    button.Url = value;
                    break;
                case nameof(CardReplyButton.PhoneNumber):
                    button.PhoneNumber = value;
                    break;
            }
            await NotifyDataChanged();
            await NotifyValidationChanged();
        }
    }

    private async Task OnButtonTypeChanged(int buttonIndex, CardReplyButtonType value)
    {
        if (buttonIndex >= 0 && buttonIndex < CardData.Buttons.Count)
        {
            CardData.Buttons[buttonIndex].Type = value;
            // Clear the opposite field when type changes
            if (value == CardReplyButtonType.WebUrl)
            {
                CardData.Buttons[buttonIndex].PhoneNumber = null;
            }
            else
            {
                CardData.Buttons[buttonIndex].Url = null;
            }
            await NotifyDataChanged();
            await NotifyValidationChanged();
        }
    }

    private string GetValidationMessage()
    {
        if (string.IsNullOrWhiteSpace(CardData.Title))
            return "Title is required.";

        bool hasAdditionalField = !string.IsNullOrWhiteSpace(CardData.ImageUrl) ||
                                 !string.IsNullOrWhiteSpace(CardData.Subtitle) ||
                                 CardData.Buttons.Any();

        if (!hasAdditionalField)
            return "At least one additional field (Image, Subtitle, or Button) must be provided.";

        if (CardData.Buttons.Count > 3)
            return "Maximum of 3 buttons allowed.";

        var invalidButton = CardData.Buttons.FirstOrDefault(b => !b.IsValid());
        if (invalidButton != null)
            return "All buttons must have a title and valid URL or phone number.";

        return string.Empty;
    }

    public async Task<bool> ValidateAsync()
    {
        if (validationsRef != null)
        {
            await validationsRef.ValidateAll();
        }
        
        await NotifyValidationChanged();
        return IsValid;
    }
}
