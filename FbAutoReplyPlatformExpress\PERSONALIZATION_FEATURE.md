# Auto-Reply Personalization Feature

## Overview

The Auto-Reply Campaign system now supports personalization tags that are automatically replaced with the commenter's actual name when processing auto-replies to Facebook comments.

## Personalization Tags

### Available Tags

- `#FIRST_NAME#` - Replaced with the commenter's first name
- `#LAST_NAME#` - Replaced with the commenter's last name

### Usage Examples

**Public Reply Message:**
```
Hi #FIRST_NAME#! Thanks for your comment. We appreciate your feedback!
```

**Private Reply Message:**
```
Hello #FIRST_NAME# #LAST_NAME#, 

Thank you for reaching out! We'll get back to you soon.

Best regards,
The Team
```

**Card Reply:**
- **Title:** `Special offer for #FIRST_NAME#!`
- **Subtitle:** `Hi #FIRST_NAME#, check out this exclusive deal just for you.`
- **Button Text:** `Get #FIRST_NAME#'s Discount`

## How It Works

### 1. UI Integration
- Personalization tag buttons are available above both Public and Private reply text areas
- Buttons insert tags at the current cursor position
- Tags are visible in the campaign configuration UI

### 2. Backend Processing Flow

1. **Webhook Processing** (`WebhookService.cs`)
   - Receives Facebook comment webhook
   - Extracts commenter's Facebook ID and name from webhook payload
   - Queues background job with commenter information

2. **Background Job Processing** (`ProcessAutoReplyJob.cs`)
   - Processes auto-reply for each comment
   - Uses `PersonalizationService` to replace tags before sending replies

3. **Personalization Service** (`PersonalizationService.cs`)
   - Attempts to fetch detailed user info from Facebook Graph API
   - Falls back to webhook name if Graph API call fails
   - Extracts first and last names from full name
   - Replaces personalization tags with actual names

### 3. Name Extraction Logic

The system intelligently extracts first and last names:

- **Single name:** "John" → First: "John", Last: ""
- **Two names:** "John Doe" → First: "John", Last: "Doe"  
- **Multiple names:** "John Michael Doe" → First: "John", Last: "Michael Doe"

### 4. Fallback Behavior

When commenter name is unavailable:
- `#FIRST_NAME#` → "Friend"
- `#LAST_NAME#` → "" (empty string)

## Technical Implementation

### Key Components

1. **PersonalizationService** - Core service for tag replacement
2. **PersonalizationTagButtons** - UI component for tag insertion
3. **ProcessAutoReplyJob** - Background job with personalization integration
4. **FacebookGraphApiService** - Enhanced to support user info fetching

### Supported Reply Types

- ✅ **Public Replies** (Comments)
- ✅ **Private Text Messages**
- ✅ **Private Card Replies** (Title, Subtitle, Button Text)

### Facebook Graph API Integration

The system attempts to fetch detailed user information using:
```
GET /{user-id}?fields=id,name,first_name,last_name&access_token={page-access-token}
```

**Note:** Due to Facebook privacy restrictions, detailed user info may not always be available. The system gracefully falls back to webhook data.

## Testing the Feature

### 1. Create Campaign with Personalization
1. Navigate to Auto-Reply Campaign creation
2. Use personalization tag buttons to insert `#FIRST_NAME#` and `#LAST_NAME#`
3. Save and activate campaign

### 2. Test with Facebook Comments
1. Post a comment on the associated Facebook post
2. System processes webhook and triggers auto-reply
3. Verify that tags are replaced with actual commenter names

### 3. Expected Results
- **Input:** "Hi #FIRST_NAME#! Thanks for commenting."
- **Commenter:** "John Doe"
- **Output:** "Hi John! Thanks for commenting."

## Error Handling

The system includes comprehensive error handling:

- **Facebook API failures:** Falls back to webhook name
- **Missing commenter name:** Uses "Friend" as fallback
- **Invalid personalization data:** Logs warnings and continues processing
- **Network issues:** Graceful degradation with fallback behavior

## Logging

Personalization activities are logged at appropriate levels:
- **Info:** Successful personalization
- **Warning:** Fallback to webhook name or default values
- **Error:** Critical failures in personalization process

## Performance Considerations

- **Caching:** Consider implementing user info caching for frequent commenters
- **Rate Limiting:** Facebook Graph API calls are subject to rate limits
- **Async Processing:** All personalization happens in background jobs to avoid blocking

## Future Enhancements

Potential improvements:
- Additional personalization tags (e.g., `#PROFILE_PICTURE#`, `#LOCATION#`)
- User preference-based personalization
- A/B testing for personalized vs. non-personalized messages
- Analytics on personalization effectiveness
