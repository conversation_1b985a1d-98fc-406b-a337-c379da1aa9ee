using FbAutoReplyPlatformExpress.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace FbAutoReplyPlatformExpress.Permissions;

public class FbAutoReplyPlatformExpressPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(FbAutoReplyPlatformExpressPermissions.GroupName);


        myGroup.AddPermission(FbAutoReplyPlatformExpressPermissions.Dashboard.Host, L("Permission:Dashboard"), MultiTenancySides.Host);

        // Facebook permissions
        var facebookPermission = myGroup.AddPermission(FbAutoReplyPlatformExpressPermissions.Facebook.Default, L("Permission:Facebook"));
        facebookPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Facebook.Connect, L("Permission:Facebook.Connect"));
        facebookPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Facebook.Disconnect, L("Permission:Facebook.Disconnect"));
        facebookPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Facebook.ViewPages, L("Permission:Facebook.ViewPages"));
        facebookPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Facebook.ManagePages, L("Permission:Facebook.ManagePages"));

        // Posts permissions
        var postsPermission = myGroup.AddPermission(FbAutoReplyPlatformExpressPermissions.Posts.Default, L("Permission:Posts"));
        postsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Posts.View, L("Permission:Posts.View"));
        postsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Posts.Sync, L("Permission:Posts.Sync"));
        postsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Posts.Create, L("Permission:Posts.Create"));
        postsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Posts.Edit, L("Permission:Posts.Edit"));
        postsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Posts.Delete, L("Permission:Posts.Delete"));
        postsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Posts.Publish, L("Permission:Posts.Publish"));
        postsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Posts.Schedule, L("Permission:Posts.Schedule"));

        // Campaigns permissions
        var campaignsPermission = myGroup.AddPermission(FbAutoReplyPlatformExpressPermissions.Campaigns.Default, L("Permission:Campaigns"));
        campaignsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Campaigns.View, L("Permission:Campaigns.View"));
        campaignsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Campaigns.Create, L("Permission:Campaigns.Create"));
        campaignsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Campaigns.Edit, L("Permission:Campaigns.Edit"));
        campaignsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Campaigns.Delete, L("Permission:Campaigns.Delete"));
        campaignsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Campaigns.Activate, L("Permission:Campaigns.Activate"));
        campaignsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Campaigns.Deactivate, L("Permission:Campaigns.Deactivate"));
        campaignsPermission.AddChild(FbAutoReplyPlatformExpressPermissions.Campaigns.ViewActivities, L("Permission:Campaigns.ViewActivities"));

        //Define your own permissions here. Example:
        //myGroup.AddPermission(FbAutoReplyPlatformExpressPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<FbAutoReplyPlatformExpressResource>(name);
    }
}
