window.insertTextAtCursor = (textareaId, text) => {
    const textarea = document.getElementById(textareaId);
    if (!textarea) {
        return;
    }

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const value = textarea.value;

    textarea.value = value.substring(0, start) + text + value.substring(end);
    textarea.selectionStart = textarea.selectionEnd = start + text.length;
    textarea.focus();

    // B<PERSON><PERSON> needs to be notified of the change
    const event = new Event('input', { bubbles: true });
    textarea.dispatchEvent(event);
};

window.getTextareaValue = (textareaId) => {
    const textarea = document.getElementById(textareaId);
    return textarea ? textarea.value : '';
};

// Functions for PostComposer emoji picker
window.getCursorPosition = (element) => {
    if (element && element.selectionStart !== undefined) {
        return element.selectionStart;
    }
    return 0;
};

window.setCursorPosition = (element, position) => {
    if (element && element.setSelectionRange) {
        element.focus();
        element.setSelectionRange(position, position);
    }
};

// Function to trigger file input click
window.triggerFileInput = (element) => {
    if (element) {
        element.click();
    }
};