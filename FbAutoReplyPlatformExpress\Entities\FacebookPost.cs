using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace FbAutoReplyPlatformExpress.Entities;

public class FacebookPost : FullAuditedAggregateRoot<Guid>
{
    [Required]
    [StringLength(256)]
    public string FacebookPostId { get; set; } = string.Empty;

    [Required]
    public Guid FacebookPageId { get; set; }

    [StringLength(2048)]
    public string Message { get; set; } = string.Empty;

    [StringLength(512)]
    public string PostType { get; set; } = string.Empty; // photo, video, link, status

    [StringLength(1024)]
    public string? AttachmentUrl { get; set; }

    [StringLength(1024)]
    public string? LinkUrl { get; set; }

    [StringLength(1024)]
    public string? PermalinkUrl { get; set; }

    [StringLength(1024)]
    public string? PictureUrl { get; set; }

    [StringLength(1024)]
    public string? FullPictureUrl { get; set; }

    public DateTime FacebookCreatedTime { get; set; }

    public int LikesCount { get; set; }

    public int CommentsCount { get; set; }

    public int SharesCount { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime? LastSyncAt { get; set; }

    // Navigation property
    public virtual FacebookPage FacebookPage { get; set; } = null!;

    protected FacebookPost()
    {
        // For EF Core
    }

    public FacebookPost(
        Guid id,
        string facebookPostId,
        Guid facebookPageId,
        string message,
        string postType,
        DateTime facebookCreatedTime) : base(id)
    {
        FacebookPostId = facebookPostId;
        FacebookPageId = facebookPageId;
        Message = message;
        PostType = postType;
        FacebookCreatedTime = facebookCreatedTime;
        IsActive = true;
    }

    public void UpdatePostStats(int likesCount, int commentsCount, int sharesCount)
    {
        LikesCount = likesCount;
        CommentsCount = commentsCount;
        SharesCount = sharesCount;
        LastSyncAt = DateTime.UtcNow;
    }

    public void UpdateAttachment(string attachmentUrl)
    {
        AttachmentUrl = attachmentUrl;
    }

    public void UpdateLink(string linkUrl)
    {
        LinkUrl = linkUrl;
    }

    public void UpdatePermalinkUrl(string permalinkUrl)
    {
        PermalinkUrl = permalinkUrl;
    }

    public void UpdatePictureUrls(string? pictureUrl, string? fullPictureUrl)
    {
        PictureUrl = pictureUrl;
        FullPictureUrl = fullPictureUrl;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public void Activate()
    {
        IsActive = true;
    }
}
