<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
    <PackageReference Include="Volo.Abp.TestBase" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FbAutoReplyPlatformExpress\FbAutoReplyPlatformExpress.csproj" />
    <ProjectReference Include="..\FbAutoReplyPlatformExpress.Contracts\FbAutoReplyPlatformExpress.Contracts.csproj" />
  </ItemGroup>

</Project>
