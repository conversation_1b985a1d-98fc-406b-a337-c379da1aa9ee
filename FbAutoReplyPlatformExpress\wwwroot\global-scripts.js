/* Your Global Scripts */

window.insertTextAtCursor = (textareaId, text) => {
    const textarea = document.getElementById(textareaId);
    if (!textarea) {
        console.warn(`Textarea with id '${textareaId}' not found`);
        return;
    }

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const value = textarea.value;

    textarea.value = value.substring(0, start) + text + value.substring(end);
    textarea.selectionStart = textarea.selectionEnd = start + text.length;
    textarea.focus();

    // <PERSON><PERSON><PERSON> needs to be notified of the change
    const event = new Event('input', { bubbles: true });
    textarea.dispatchEvent(event);
};

window.getTextareaValue = (textareaId) => {
    const textarea = document.getElementById(textareaId);
    return textarea ? textarea.value : '';
};
