﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FbAutoReplyPlatformExpress.Migrations
{
    /// <inheritdoc />
    public partial class UpdateMaxRepliesPerUserDefaultValue : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update existing campaigns that have the old default value of 1 to use 0 (unlimited)
            migrationBuilder.Sql("UPDATE AppAutoReplyCampaigns SET MaxRepliesPerUser = 0 WHERE MaxRepliesPerUser = 1");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Revert: Set campaigns back to 1 if they were changed from 1 to 0
            // Note: This is a best-effort revert since we can't distinguish between campaigns that were originally 1 vs 0
            migrationBuilder.Sql("UPDATE AppAutoReplyCampaigns SET MaxRepliesPerUser = 1 WHERE MaxRepliesPerUser = 0");
        }
    }
}
