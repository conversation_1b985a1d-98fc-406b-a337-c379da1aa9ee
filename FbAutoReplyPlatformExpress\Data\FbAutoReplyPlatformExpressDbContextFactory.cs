﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace FbAutoReplyPlatformExpress.Data;

public class FbAutoReplyPlatformExpressDbContextFactory : IDesignTimeDbContextFactory<FbAutoReplyPlatformExpressDbContext>
{
    public FbAutoReplyPlatformExpressDbContext CreateDbContext(string[] args)
    {
        var configuration = BuildConfiguration();

        var builder = new DbContextOptionsBuilder<FbAutoReplyPlatformExpressDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));

        return new FbAutoReplyPlatformExpressDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}