<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>FbAutoReplyPlatformExpress</RootNamespace>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\FbAutoReplyPlatformExpress\*.json" />
    <Content Remove="Localization\FbAutoReplyPlatformExpress\*.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Validation" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AspNetCore.Components" Version="9.1.1" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Pro.Admin.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Pro.Public.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Pro.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.OpenIddict.Pro.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.LanguageManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TextTemplateManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.AuditLogging.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.1.1" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="$(UserProfile)\.nuget\packages\*\*\contentFiles\any\*\*.abppkg*" />
  </ItemGroup>

</Project>
