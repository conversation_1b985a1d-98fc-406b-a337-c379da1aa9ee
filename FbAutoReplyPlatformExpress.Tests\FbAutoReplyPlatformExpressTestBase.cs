using Microsoft.Extensions.DependencyInjection;
using Moq;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Services.Dtos;

namespace FbAutoReplyPlatformExpress.Tests;

public abstract class FbAutoReplyPlatformExpressTestBase : IDisposable
{
    protected FacebookUser CreateTestFacebookUser(Guid? userId = null)
    {
        var testUserId = userId ?? Guid.NewGuid();
        return new FacebookUser(
            Guid.NewGuid(),
            "test_facebook_id",
            testUserId,
            "test_access_token",
            "<EMAIL>",
            "Test User"
        );
    }

    protected FacebookPage CreateTestFacebookPage(Guid facebookUserId)
    {
        return new FacebookPage(
            Guid.NewGuid(),
            "test_page_id",
            facebookUserId,
            "Test Page",
            "test_page_access_token"
        );
    }

    protected ScheduledFacebookPost CreateTestScheduledPost(Guid facebookPageId)
    {
        return new ScheduledFacebookPost(
            Guid.NewGuid(),
            facebookPageId,
            FacebookPostType.Text,
            "Test message",
            new List<string>(),
            null,
            DateTime.UtcNow.AddHours(1)
        );
    }

    protected Mock<ICurrentUser> CreateMockCurrentUser(Guid userId)
    {
        var currentUserMock = new Mock<ICurrentUser>();
        currentUserMock.Setup(x => x.Id).Returns(userId);
        currentUserMock.Setup(x => x.IsAuthenticated).Returns(true);
        return currentUserMock;
    }

    public virtual void Dispose()
    {
        // Cleanup if needed
    }
}
