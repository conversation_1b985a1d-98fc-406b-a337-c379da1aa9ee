@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@inject IJSRuntime JSRuntime

<Card>
    <CardHeader>
        <h6 class="mb-0">
            <Icon Name="IconName.Clock" class="me-2" />
            Publishing Schedule
        </h6>
    </CardHeader>
    <CardBody>
        <!-- Publishing Options -->
        <Field>
            <FieldLabel>When to Publish</FieldLabel>
            <RadioGroup TValue="bool" @bind-Value="PublishNow" @onchange="OnPublishOptionChanged">
                <Radio TValue="bool" Value="true">
                    <Icon Name="IconName.Play" class="me-2" />
                    Publish Now
                </Radio>
                <Radio TValue="bool" Value="false">
                    <Icon Name="IconName.Clock" class="me-2" />
                    Schedule for Later
                </Radio>
            </RadioGroup>
        </Field>

        <!-- Scheduled Date/Time Selection -->
        @if (!PublishNow)
        {
            <div class="scheduled-options mt-3">
                <!-- Date Selection -->
                <Field>
                    <FieldLabel>Date</FieldLabel>
                    <DateEdit TValue="DateTime?" 
                             @bind-Date="ScheduledDate" 
                             Min="@MinDate"
                             Max="@MaxDate"
                             @onchange="OnScheduledDateChanged" />
                    <FieldHelp>Select the date to publish this post</FieldHelp>
                </Field>

                <!-- Time Selection -->
                <Field>
                    <FieldLabel>Time</FieldLabel>
                    <TimeEdit TValue="TimeSpan?" 
                             @bind-Time="ScheduledTime" 
                             @onchange="OnScheduledTimeChanged" />
                    <FieldHelp>Select the time to publish this post</FieldHelp>
                </Field>

                <!-- Timezone Display -->
                <Field>
                    <FieldLabel>Timezone</FieldLabel>
                    <div class="timezone-display">
                        <Icon Name="IconName.Globe" class="me-2" />
                        <span>@CurrentTimezone</span>
                        <small class="text-muted ms-2">
                            (UTC@(TimezoneOffset >= 0 ? "+" : "")@TimezoneOffset:00)
                        </small>
                    </div>
                    <FieldHelp>Posts will be published in your local timezone</FieldHelp>
                </Field>

                <!-- Scheduled Summary -->
                @if (GetScheduledDateTime().HasValue)
                {
                    <Alert Color="Color.Info" class="mt-3">
                        <Icon Name="IconName.InfoCircle" class="me-2" />
                        <strong>Scheduled for:</strong> @GetScheduledDateTime().Value.ToString("dddd, MMMM d, yyyy 'at' h:mm tt")
                        <br />
                        <small class="text-muted">
                            @GetTimeUntilPublish()
                        </small>
                    </Alert>
                }

                <!-- Validation Messages -->
                @if (!string.IsNullOrEmpty(ValidationMessage))
                {
                    <Alert Color="Color.Warning" class="mt-3">
                        <Icon Name="IconName.ExclamationTriangle" class="me-2" />
                        @ValidationMessage
                    </Alert>
                }
            </div>
        }

        <!-- Publishing Guidelines -->
        <div class="publishing-guidelines mt-4">
            <h6 class="text-muted mb-2">
                <Icon Name="IconName.InfoCircle" class="me-2" />
                Publishing Guidelines
            </h6>
            <ul class="small text-muted mb-0">
                <li>Posts can be scheduled up to 6 months in advance</li>
                <li>Scheduled posts must be at least 10 minutes in the future</li>
                <li>All times are in your local timezone</li>
                <li>Facebook may delay publishing by a few minutes</li>
                @if (!PublishNow)
                {
                    <li class="text-primary">You can edit or cancel scheduled posts before they're published</li>
                }
            </ul>
        </div>
    </CardBody>
</Card>

@code {
    [Parameter] public bool PublishNow { get; set; } = true;
    [Parameter] public EventCallback<bool> PublishNowChanged { get; set; }
    [Parameter] public DateTime? ScheduledPublishTime { get; set; }
    [Parameter] public EventCallback<DateTime?> ScheduledPublishTimeChanged { get; set; }
    [Parameter] public EventCallback OnScheduleChanged { get; set; }

    private DateTime? ScheduledDate;
    private TimeSpan? ScheduledTime;
    private string CurrentTimezone = "";
    private int TimezoneOffset = 0;
    private string? ValidationMessage;

    private DateTime MinDate => DateTime.Today.AddDays(0);
    private DateTime MaxDate => DateTime.Today.AddMonths(6);

    protected override async Task OnInitializedAsync()
    {
        // Get user's timezone information
        try
        {
            var timezoneInfo = await JSRuntime.InvokeAsync<TimezoneInfo>("getTimezoneInfo");
            CurrentTimezone = timezoneInfo.Name;
            TimezoneOffset = timezoneInfo.Offset;
        }
        catch
        {
            CurrentTimezone = "Local Time";
            TimezoneOffset = 0;
        }

        // Initialize scheduled date/time if provided
        if (ScheduledPublishTime.HasValue)
        {
            ScheduledDate = ScheduledPublishTime.Value.Date;
            ScheduledTime = ScheduledPublishTime.Value.TimeOfDay;
            PublishNow = false;
        }
        else
        {
            // Default to tomorrow at current time + 1 hour
            var defaultTime = DateTime.Now.AddHours(1);
            ScheduledDate = defaultTime.Date;
            ScheduledTime = defaultTime.TimeOfDay;
        }
    }

    private async Task OnPublishOptionChanged(ChangeEventArgs e)
    {
        if (bool.TryParse(e.Value?.ToString(), out var publishNow))
        {
            PublishNow = publishNow;
            await PublishNowChanged.InvokeAsync(PublishNow);
            
            if (PublishNow)
            {
                await ScheduledPublishTimeChanged.InvokeAsync(null);
            }
            else
            {
                await UpdateScheduledDateTime();
            }
            
            await OnScheduleChanged.InvokeAsync();
        }
    }

    private async Task OnScheduledDateChanged()
    {
        await UpdateScheduledDateTime();
    }

    private async Task OnScheduledTimeChanged()
    {
        await UpdateScheduledDateTime();
    }

    private async Task UpdateScheduledDateTime()
    {
        ValidationMessage = null;

        if (!PublishNow && ScheduledDate.HasValue && ScheduledTime.HasValue)
        {
            var scheduledDateTime = ScheduledDate.Value.Date.Add(ScheduledTime.Value);
            
            // Validate the scheduled time
            if (scheduledDateTime <= DateTime.Now.AddMinutes(10))
            {
                ValidationMessage = "Scheduled time must be at least 10 minutes in the future.";
                return;
            }

            if (scheduledDateTime > DateTime.Now.AddMonths(6))
            {
                ValidationMessage = "Posts cannot be scheduled more than 6 months in advance.";
                return;
            }

            await ScheduledPublishTimeChanged.InvokeAsync(scheduledDateTime);
            await OnScheduleChanged.InvokeAsync();
        }
    }

    private DateTime? GetScheduledDateTime()
    {
        if (!PublishNow && ScheduledDate.HasValue && ScheduledTime.HasValue)
        {
            return ScheduledDate.Value.Date.Add(ScheduledTime.Value);
        }
        return null;
    }

    private string GetTimeUntilPublish()
    {
        var scheduledTime = GetScheduledDateTime();
        if (!scheduledTime.HasValue) return "";

        var timeUntil = scheduledTime.Value - DateTime.Now;
        
        if (timeUntil.TotalDays >= 1)
        {
            var days = (int)timeUntil.TotalDays;
            var hours = timeUntil.Hours;
            return $"In {days} day{(days != 1 ? "s" : "")} and {hours} hour{(hours != 1 ? "s" : "")}";
        }
        else if (timeUntil.TotalHours >= 1)
        {
            var hours = (int)timeUntil.TotalHours;
            var minutes = timeUntil.Minutes;
            return $"In {hours} hour{(hours != 1 ? "s" : "")} and {minutes} minute{(minutes != 1 ? "s" : "")}";
        }
        else if (timeUntil.TotalMinutes >= 1)
        {
            var minutes = (int)timeUntil.TotalMinutes;
            return $"In {minutes} minute{(minutes != 1 ? "s" : "")}";
        }
        else
        {
            return "Very soon";
        }
    }

    public class TimezoneInfo
    {
        public string Name { get; set; } = "";
        public int Offset { get; set; }
    }
}

<style>
    .scheduled-options {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }

    .timezone-display {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background-color: #e9ecef;
        border-radius: 4px;
        font-weight: 500;
    }

    .publishing-guidelines {
        border-top: 1px solid #dee2e6;
        padding-top: 15px;
    }

    .publishing-guidelines ul {
        padding-left: 20px;
    }

    .publishing-guidelines li {
        margin-bottom: 4px;
    }
</style>

<script>
    window.getTimezoneInfo = () => {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const offset = -new Date().getTimezoneOffset() / 60;
        
        return {
            name: timezone.replace('_', ' '),
            offset: offset
        };
    };
</script>
