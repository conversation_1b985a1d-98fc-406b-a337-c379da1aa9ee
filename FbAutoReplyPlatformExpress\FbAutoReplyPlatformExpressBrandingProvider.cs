﻿using Microsoft.Extensions.Localization;
using FbAutoReplyPlatformExpress.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace FbAutoReplyPlatformExpress;

[Dependency(ReplaceServices = true)]
public class FbAutoReplyPlatformExpressBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<FbAutoReplyPlatformExpressResource> _localizer;

    public FbAutoReplyPlatformExpressBrandingProvider(IStringLocalizer<FbAutoReplyPlatformExpressResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}
