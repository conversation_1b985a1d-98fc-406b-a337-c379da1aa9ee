@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web

<div class="emoji-picker-container">
    <Button Color="Color.Secondary" @onclick="ToggleEmojiPicker">😀</Button>
    @if (IsPickerVisible)
    {
        <div class="emoji-picker @(Alignment == PickerAlignment.Right ? "emoji-picker-right" : "")">
            @foreach (var emoji in Emojis)
            {
                <span class="emoji" @onclick="() => SelectEmoji(emoji)">@emoji</span>
            }
        </div>
    }
</div>

@code {
    [Parameter]
    public EventCallback<string> OnEmojiSelected { get; set; }

    [Parameter]
    public PickerAlignment Alignment { get; set; } = PickerAlignment.Left;

    private bool IsPickerVisible = false;

    private List<string> Emojis { get; } = new()
    {
        "😀", "😁", "😂", "🤣", "😃", "😄", "😅", "😆", "😉", "😊",
        "😋", "😎", "😍", "😘", "🥰", "😗", "😙", "😚", "🙂", "🤗",
        "🤩", "🤔", "🤨", "😐", "😑", "😶", "🙄", "😏", "😣", "😥",
        "😮", "🤐", "😯", "😪", "😫", "😴", "😌", "😛", "😜", "😝",
        "🤤", "😒", "😓", "😔", "😕", "🙃", "🤑", "😲", "☹️", "🙁",
        "😖", "😞", "😟", "😤", "😢", "😭", "😦", "😧", "😨", "😩",
        "🤯", "😬", "😰", "😱", "🥵", "🥶", "😳", "🤪", "😵", "😡",
        "😠", "🤬", "😷", "🤒", "🤕", "🤢", "🤮", "🤧", "😇", "🤠",
        "🤡", "🥳", "🥴", "🥺", "🤥", "🤫", "🤭", "🧐", "🤓", "👍",
        "👎", "❤️", "💔", "💕", "💖", "💗", "💓", "💞", "💝", "💘"
    };

    private void ToggleEmojiPicker()
    {
        IsPickerVisible = !IsPickerVisible;
    }

    private async Task SelectEmoji(string emoji)
    {
        await OnEmojiSelected.InvokeAsync(emoji);
        IsPickerVisible = false;
    }

    public enum PickerAlignment
    {
        Left,
        Right
    }
}

<style>
    .emoji-picker-container {
        position: relative;
        display: inline-block;
    }

    .emoji-picker {
        position: absolute;
        bottom: 100%;
        left: 0;
        z-index: 1000;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        width: 300px;
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #ccc;
        background-color: white;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .emoji-picker-right {
        left: auto;
        right: 0;
    }

    .emoji {
        cursor: pointer;
        font-size: 1.5rem;
        padding: 2px;
        border-radius: 5px;
        transition: background-color 0.2s;
    }

    .emoji:hover {
        background-color: #f0f0f0;
    }
</style> 