﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FbAutoReplyPlatformExpress.Migrations
{
    /// <inheritdoc />
    public partial class AddPostEnhancedFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FullPictureUrl",
                table: "AppFacebookPosts",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PermalinkUrl",
                table: "AppFacebookPosts",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PictureUrl",
                table: "AppFacebookPosts",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FullPictureUrl",
                table: "AppFacebookPosts");

            migrationBuilder.DropColumn(
                name: "PermalinkUrl",
                table: "AppFacebookPosts");

            migrationBuilder.DropColumn(
                name: "PictureUrl",
                table: "AppFacebookPosts");
        }
    }
}
