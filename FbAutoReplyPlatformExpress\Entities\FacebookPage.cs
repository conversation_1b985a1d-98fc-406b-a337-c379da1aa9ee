using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace FbAutoReplyPlatformExpress.Entities;

public class FacebookPage : FullAuditedAggregateRoot<Guid>
{
    [Required]
    [StringLength(256)]
    public string FacebookPageId { get; set; } = string.Empty;

    [Required]
    public Guid FacebookUserId { get; set; }

    [Required]
    [StringLength(256)]
    public string PageName { get; set; } = string.Empty;

    [StringLength(512)]
    public string PageAccessToken { get; set; } = string.Empty;

    [StringLength(512)]
    public string PageProfilePictureUrl { get; set; } = string.Empty;

    [StringLength(256)]
    public string Category { get; set; } = string.Empty;

    public int FollowersCount { get; set; }

    public bool IsConnected { get; set; } = true;

    public bool WebhookSubscribed { get; set; } = false;

    [StringLength(1024)]
    public string? WebhookSubscriptionId { get; set; }

    public DateTime? LastSyncAt { get; set; }

    // Navigation property
    public virtual FacebookUser FacebookUser { get; set; } = null!;

    protected FacebookPage()
    {
        // For EF Core
    }

    public FacebookPage(
        Guid id,
        string facebookPageId,
        Guid facebookUserId,
        string pageName,
        string pageAccessToken) : base(id)
    {
        FacebookPageId = facebookPageId;
        FacebookUserId = facebookUserId;
        PageName = pageName;
        PageAccessToken = pageAccessToken;
        IsConnected = true;
        WebhookSubscribed = false;
    }

    public void UpdatePageInfo(string pageName, string profilePictureUrl, string category, int followersCount)
    {
        PageName = pageName;
        PageProfilePictureUrl = profilePictureUrl;
        Category = category;
        FollowersCount = followersCount;
        LastSyncAt = DateTime.UtcNow;
    }

    public void UpdateAccessToken(string pageAccessToken)
    {
        PageAccessToken = pageAccessToken;
    }

    public void SetWebhookSubscription(string subscriptionId)
    {
        WebhookSubscribed = true;
        WebhookSubscriptionId = subscriptionId;
    }

    public void RemoveWebhookSubscription()
    {
        WebhookSubscribed = false;
        WebhookSubscriptionId = null;
    }

    public void Disconnect()
    {
        IsConnected = false;
        WebhookSubscribed = false;
        WebhookSubscriptionId = null;
    }

    public void Reconnect()
    {
        IsConnected = true;
    }
}
