using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Volo.Abp.Application.Services;

namespace FbAutoReplyPlatformExpress.Services;

public interface IWebhookService : IApplicationService
{
    Task<string> VerifyWebhookAsync(string mode, string token, string challenge);
    Task ProcessWebhookAsync(string payload);
    Task ProcessCommentAsync(WebhookCommentDto comment);
}
