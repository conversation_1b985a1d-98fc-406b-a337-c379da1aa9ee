using System.ComponentModel.DataAnnotations;
using Xunit;
using FbAutoReplyPlatformExpress.Services.Dtos;
using FbAutoReplyPlatformExpress.Entities;

namespace FbAutoReplyPlatformExpress.Tests;

public class BasicValidationTests
{
    [Fact]
    public void PublishFacebookPostDto_WithValidTextPost_ShouldBeValid()
    {
        // Arrange
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Text,
            Message = "Valid test message",
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert
        Assert.True(isValid);
    }

    [Fact]
    public void PublishFacebookPostDto_WithEmptyMessage_ShouldBeInvalid()
    {
        // Arrange
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Text,
            Message = "",
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void PublishFacebookPostDto_WithImagePostAndMedia_ShouldBeValid()
    {
        // Arrange
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Image,
            Message = "Image post with media",
            Media = new List<FacebookPostMediaDto>
            {
                new FacebookPostMediaDto
                {
                    FileName = "image.jpg",
                    ContentType = "image/jpeg",
                    Content = new byte[] { 1, 2, 3, 4 },
                    Size = 4
                }
            },
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert
        Assert.True(isValid);
    }

    [Fact]
    public void PublishFacebookPostDto_WithLinkPostAndUrl_ShouldBeValid()
    {
        // Arrange
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Link,
            Message = "Link post with URL",
            LinkUrl = "https://example.com",
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert
        Assert.True(isValid);
    }

    [Fact]
    public void PublishFacebookPostDto_WithScheduledPostAndTime_ShouldBeValid()
    {
        // Arrange
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Text,
            Message = "Scheduled post with time",
            PublishNow = false,
            ScheduledPublishTime = DateTime.UtcNow.AddHours(1)
        };

        // Act
        var isValid = dto.IsValid;

        // Assert
        Assert.True(isValid);
    }

    [Fact]
    public void PublishFacebookPostDto_WithScheduledPostInPast_ShouldStillBeValid()
    {
        // Arrange - The DTO's IsValid property only checks content, not scheduling time
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Text,
            Message = "Scheduled post in past",
            PublishNow = false,
            ScheduledPublishTime = DateTime.UtcNow.AddHours(-1)
        };

        // Act
        var isValid = dto.IsValid;

        // Assert - IsValid only checks if content is provided, not scheduling validation
        Assert.True(isValid);
    }

    [Fact]
    public void ScheduledFacebookPost_Creation_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var id = Guid.NewGuid();
        var facebookPageId = Guid.NewGuid();
        var postType = FacebookPostType.Text;
        var message = "Test message";
        var mediaUrls = new List<string> { "https://example.com/image.jpg" };
        var linkUrl = "https://example.com";
        var scheduledTime = DateTime.UtcNow.AddHours(2);

        // Act
        var scheduledPost = new ScheduledFacebookPost(
            id,
            facebookPageId,
            postType,
            message,
            mediaUrls,
            linkUrl,
            scheduledTime
        );

        // Assert
        Assert.Equal(id, scheduledPost.Id);
        Assert.Equal(facebookPageId, scheduledPost.FacebookPageId);
        Assert.Equal(postType, scheduledPost.PostType);
        Assert.Equal(message, scheduledPost.Message);
        Assert.Equal(mediaUrls, scheduledPost.MediaUrls);
        Assert.Equal(linkUrl, scheduledPost.LinkUrl);
        Assert.Equal(scheduledTime, scheduledPost.ScheduledPublishTime);
        Assert.Equal(PostScheduleStatus.Scheduled, scheduledPost.Status);
    }

    [Fact]
    public void FacebookPostType_EnumValues_ShouldBeCorrect()
    {
        // Assert
        Assert.Equal(0, (int)FacebookPostType.Text);
        Assert.Equal(1, (int)FacebookPostType.Image);
        Assert.Equal(2, (int)FacebookPostType.Video);
        Assert.Equal(3, (int)FacebookPostType.Link);
    }

    [Fact]
    public void PostScheduleStatus_EnumValues_ShouldBeCorrect()
    {
        // Assert
        Assert.Equal(0, (int)PostScheduleStatus.Draft);
        Assert.Equal(1, (int)PostScheduleStatus.Scheduled);
        Assert.Equal(2, (int)PostScheduleStatus.Published);
        Assert.Equal(3, (int)PostScheduleStatus.Failed);
        Assert.Equal(4, (int)PostScheduleStatus.Cancelled);
    }

    [Fact]
    public void FacebookPostMediaDto_Properties_ShouldWorkCorrectly()
    {
        // Arrange
        var mediaDto = new FacebookPostMediaDto
        {
            FileName = "test.jpg",
            ContentType = "image/jpeg",
            Content = new byte[] { 1, 2, 3 },
            Size = 3
        };

        // Assert
        Assert.Equal("test.jpg", mediaDto.FileName);
        Assert.Equal("image/jpeg", mediaDto.ContentType);
        Assert.Equal(3, mediaDto.Content.Length);
        Assert.Equal(3, mediaDto.Size);
        Assert.True(mediaDto.IsImage);
        Assert.False(mediaDto.IsVideo);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public void PublishFacebookPostDto_WithInvalidMessage_ShouldBeInvalid(string message)
    {
        // Arrange
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Text,
            Message = message,
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert
        Assert.False(isValid);
    }

    [Theory]
    [InlineData("https://example.com")]
    [InlineData("http://test.com")]
    [InlineData("https://www.facebook.com/page")]
    public void PublishFacebookPostDto_WithValidUrls_ShouldBeValid(string url)
    {
        // Arrange
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Link,
            Message = "Link post",
            LinkUrl = url,
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert
        Assert.True(isValid);
    }

    [Theory]
    [InlineData("not-a-url")]
    [InlineData("ftp://invalid")]
    public void PublishFacebookPostDto_WithInvalidUrls_ShouldStillBeValid(string url)
    {
        // Arrange - The DTO's IsValid property only checks if LinkUrl is not null/empty, not format
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Link,
            LinkUrl = url,
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert - IsValid only checks if LinkUrl has content, not URL format validation
        Assert.True(isValid);
    }

    [Fact]
    public void PublishFacebookPostDto_WithEmptyLinkUrl_ShouldBeInvalid()
    {
        // Arrange - No message, no media, and empty LinkUrl should be invalid
        var dto = new PublishFacebookPostDto
        {
            FacebookPageId = Guid.NewGuid(),
            PostType = FacebookPostType.Link,
            Message = null, // No message
            Media = new List<FacebookPostMediaDto>(), // No media
            LinkUrl = "", // Empty LinkUrl
            PublishNow = true
        };

        // Act
        var isValid = dto.IsValid;

        // Assert - Should be invalid since no content is provided
        Assert.False(isValid);
    }
}
