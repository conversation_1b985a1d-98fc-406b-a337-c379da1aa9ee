# Facebook Post Publishing Feature - Test Suite

## Overview
This test suite provides comprehensive validation for the Facebook Post Publishing feature implemented in the FbAutoReplyPlatformExpress platform. The tests cover DTO validation, entity creation, and core business logic validation.

## Test Structure

### Test Project Configuration
- **Framework**: xUnit with .NET 9.0
- **Dependencies**: 
  - `xunit` (2.9.2)
  - `xunit.runner.visualstudio` (2.8.2)
  - `Microsoft.NET.Test.Sdk` (17.12.0)
  - `Moq` (4.20.72)
  - ABP Framework packages (9.1.1)

### Test Categories

#### 1. DTO Validation Tests (BasicValidationTests.cs)
**Purpose**: Validate the `PublishFacebookPostDto` validation logic and property behavior.

**Test Coverage**:
- ✅ Valid post scenarios (text, image, video, link posts)
- ✅ Invalid post scenarios (no content provided)
- ✅ Media handling and validation
- ✅ Scheduling validation behavior
- ✅ URL handling (format validation is handled at service level, not DTO level)

#### 2. Entity Creation Tests
**Purpose**: Validate entity constructors and property assignments.

**Test Coverage**:
- ✅ `FacebookUser` entity creation with correct constructor parameters
- ✅ `FacebookPage` entity creation with proper relationships
- ✅ `ScheduledFacebookPost` entity creation with all required fields

#### 3. Enum Validation Tests
**Purpose**: Ensure enum values are correctly defined and accessible.

**Test Coverage**:
- ✅ `FacebookPostType` enum values (Text=0, Image=1, Video=2, Link=3)
- ✅ `PostScheduleStatus` enum values (Draft=0, Scheduled=1, Published=2, Failed=3, Cancelled=4)

#### 4. Media DTO Tests
**Purpose**: Validate `FacebookPostMediaDto` structure and helper properties.

**Test Coverage**:
- ✅ Property assignments (FileName, ContentType, Content, Size)
- ✅ Helper properties (`IsImage`, `IsVideo` based on ContentType)

## Key Test Insights

### DTO Validation Logic
The `PublishFacebookPostDto.IsValid` property implements simple content validation:
```csharp
public bool IsValid => !string.IsNullOrWhiteSpace(Message) || Media.Any() || !string.IsNullOrWhiteSpace(LinkUrl);
```

**Important Notes**:
- ✅ **Content Validation Only**: Checks if at least one content type is provided
- ❌ **No URL Format Validation**: URL format validation is handled at service level
- ❌ **No Schedule Time Validation**: Scheduling validation is handled at service level
- ❌ **No Media Content Validation**: Media validation is handled during upload/processing

### Entity Constructor Patterns
All entities follow ABP Framework patterns with proper constructor signatures:

```csharp
// FacebookUser constructor
FacebookUser(Guid id, string facebookId, Guid userId, string accessToken, string facebookEmail, string facebookName)

// FacebookPage constructor  
FacebookPage(Guid id, string facebookPageId, Guid facebookUserId, string pageName, string pageAccessToken)

// ScheduledFacebookPost constructor
ScheduledFacebookPost(Guid id, Guid facebookPageId, FacebookPostType postType, string? message, List<string>? mediaUrls, string? linkUrl, DateTime scheduledPublishTime)
```

## Running Tests

### Command Line
```bash
# Run all tests
dotnet test FbAutoReplyPlatformExpress.Tests

# Run with detailed output
dotnet test FbAutoReplyPlatformExpress.Tests --verbosity normal

# Run specific test class
dotnet test FbAutoReplyPlatformExpress.Tests --filter "FullyQualifiedName~BasicValidationTests"
```

### Test Results Summary
- **Total Tests**: 18
- **Passed**: 18 ✅
- **Failed**: 0 ❌
- **Skipped**: 0 ⏭️

## Integration with CI/CD
These tests are designed to be run as part of the build pipeline to ensure:
1. DTO validation logic remains consistent
2. Entity constructors maintain compatibility
3. Enum values don't change unexpectedly
4. Core business logic validation works correctly

## Future Test Enhancements
Consider adding:
1. **Integration Tests**: Test actual Facebook Graph API integration
2. **Service Layer Tests**: Test `FacebookPostPublishingService` methods
3. **Repository Tests**: Test data persistence and retrieval
4. **Background Job Tests**: Test scheduled post processing
5. **Validation Tests**: Test comprehensive input validation at service level
