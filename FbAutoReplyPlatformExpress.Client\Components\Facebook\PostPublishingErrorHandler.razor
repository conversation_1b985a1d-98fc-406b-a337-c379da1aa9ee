@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components
@inherits FbAutoReplyPlatformExpressComponentBase
@inject NavigationManager NavigationManager
@inject IFacebookAuthService FacebookAuthService
@inject IMessageService MessageService

<div class="post-publishing-error-handler">
    @if (ShowErrorAlert && PublishResult != null && !PublishResult.Success)
    {
        <Alert Color="@GetAlertColor()" Visible="true" Class="mb-3">
            <AlertMessage>
                <Icon Name="@GetAlertIcon()" class="me-2" />
                <strong>@GetAlertTitle()</strong>
            </AlertMessage>
            <AlertDescription>
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1 me-3">
                        <div>@GetUserFriendlyErrorMessage()</div>
                        
                        @if (ShowSuggestions)
                        {
                            <div class="mt-2">
                                <small class="text-muted">
                                    <strong>Suggestions:</strong>
                                    <ul class="mb-0 mt-1">
                                        @foreach (var suggestion in GetErrorSuggestions())
                                        {
                                            <li>@suggestion</li>
                                        }
                                    </ul>
                                </small>
                            </div>
                        }
                        
                        @if (ShowTechnicalDetails && !string.IsNullOrEmpty(PublishResult.ErrorMessage))
                        {
                            <details class="mt-2">
                                <summary class="text-muted" style="cursor: pointer;">Technical Details</summary>
                                <small class="text-muted mt-1 d-block">@PublishResult.ErrorMessage</small>
                            </details>
                        }
                    </div>
                    <div class="d-flex flex-column gap-2">
                        @if (ShowReconnectButton && RequiresReconnection())
                        {
                            <Button Color="Color.Primary" Size="Size.Small" Clicked="@HandleReconnectClicked">
                                <Icon Name="IconName.Link" class="me-1" />
                                Reconnect Facebook
                            </Button>
                        }
                        @if (ShowRetryButton && CanRetry())
                        {
                            <Button Color="Color.Secondary" Size="Size.Small" Clicked="@HandleRetryClicked" Disabled="@IsRetrying">
                                @if (IsRetrying)
                                {
                                    <div class="spinner-border spinner-border-sm me-1" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                }
                                else
                                {
                                    <Icon Name="IconName.Redo" class="me-1" />
                                }
                                Retry Publishing
                            </Button>
                        }
                        @if (ShowEditButton)
                        {
                            <Button Color="Color.Info" Size="Size.Small" Clicked="@HandleEditClicked">
                                <Icon Name="IconName.Edit" class="me-1" />
                                Edit Post
                            </Button>
                        }
                        @if (ShowDismissButton)
                        {
                            <Button Color="Color.Light" Size="Size.Small" Clicked="@HandleDismissClicked">
                                <Icon Name="IconName.Times" />
                            </Button>
                        }
                    </div>
                </div>
            </AlertDescription>
        </Alert>
    }
</div>

@code {
    [Parameter] public FacebookPostPublishResultDto? PublishResult { get; set; }
    [Parameter] public bool ShowErrorAlert { get; set; } = true;
    [Parameter] public bool ShowReconnectButton { get; set; } = true;
    [Parameter] public bool ShowRetryButton { get; set; } = true;
    [Parameter] public bool ShowEditButton { get; set; } = true;
    [Parameter] public bool ShowDismissButton { get; set; } = true;
    [Parameter] public bool ShowTechnicalDetails { get; set; } = false;
    [Parameter] public bool ShowSuggestions { get; set; } = true;
    [Parameter] public EventCallback OnReconnectClicked { get; set; }
    [Parameter] public EventCallback OnRetryClicked { get; set; }
    [Parameter] public EventCallback OnEditClicked { get; set; }
    [Parameter] public EventCallback OnDismissClicked { get; set; }

    private bool IsRetrying { get; set; } = false;

    private Color GetAlertColor()
    {
        if (PublishResult?.ErrorType == null) return Color.Danger;
        
        return PublishResult.ErrorType switch
        {
            FacebookErrorType.TokenExpired => Color.Danger,
            FacebookErrorType.TokenInvalid => Color.Danger,
            FacebookErrorType.MissingPermissions => Color.Warning,
            FacebookErrorType.ConnectionLost => Color.Warning,
            FacebookErrorType.RateLimited => Color.Info,
            FacebookErrorType.General => Color.Secondary,
            _ => Color.Secondary
        };
    }

    private IconName GetAlertIcon()
    {
        if (PublishResult?.ErrorType == null) return IconName.ExclamationTriangle;
        
        return PublishResult.ErrorType switch
        {
            FacebookErrorType.TokenExpired => IconName.ExclamationTriangle,
            FacebookErrorType.TokenInvalid => IconName.Times,
            FacebookErrorType.MissingPermissions => IconName.ExclamationTriangle,
            FacebookErrorType.ConnectionLost => IconName.Times,
            FacebookErrorType.RateLimited => IconName.Clock,
            FacebookErrorType.General => IconName.Info,
            _ => IconName.Info
        };
    }

    private string GetAlertTitle()
    {
        if (PublishResult?.ErrorType == null) return "Publishing Failed";
        
        return PublishResult.ErrorType switch
        {
            FacebookErrorType.TokenExpired => "Facebook Token Expired",
            FacebookErrorType.TokenInvalid => "Facebook Token Invalid",
            FacebookErrorType.MissingPermissions => "Missing Facebook Permissions",
            FacebookErrorType.ConnectionLost => "Facebook Connection Lost",
            FacebookErrorType.RateLimited => "Facebook Rate Limit Reached",
            FacebookErrorType.General => "Publishing Failed",
            _ => "Publishing Failed"
        };
    }

    private string GetUserFriendlyErrorMessage()
    {
        if (PublishResult?.ErrorType == null) 
            return "An unexpected error occurred while publishing your post.";
        
        return PublishResult.ErrorType switch
        {
            FacebookErrorType.TokenExpired => "Your Facebook access token has expired. Please reconnect your Facebook account to continue publishing posts.",
            FacebookErrorType.TokenInvalid => "Your Facebook access token is invalid. Please reconnect your Facebook account.",
            FacebookErrorType.MissingPermissions => "Your Facebook account doesn't have the required permissions to publish posts. Please reconnect with the necessary permissions.",
            FacebookErrorType.ConnectionLost => "Connection to Facebook was lost. Please check your internet connection and try again.",
            FacebookErrorType.RateLimited => "You've reached Facebook's rate limit for publishing posts. Please wait a few minutes before trying again.",
            FacebookErrorType.General => PublishResult.ErrorMessage ?? "An error occurred while publishing your post. Please try again.",
            _ => "An unexpected error occurred while publishing your post."
        };
    }

    private List<string> GetErrorSuggestions()
    {
        if (PublishResult?.ErrorType == null) 
            return new List<string> { "Try publishing the post again", "Check your internet connection" };
        
        return PublishResult.ErrorType switch
        {
            FacebookErrorType.TokenExpired => new List<string>
            {
                "Click 'Reconnect Facebook' to refresh your access token",
                "Make sure you grant all required permissions during reconnection"
            },
            FacebookErrorType.TokenInvalid => new List<string>
            {
                "Reconnect your Facebook account",
                "Ensure you're using the correct Facebook account"
            },
            FacebookErrorType.MissingPermissions => new List<string>
            {
                "Reconnect Facebook and grant 'pages_manage_posts' permission",
                "Make sure you're an admin of the Facebook page you're trying to post to"
            },
            FacebookErrorType.ConnectionLost => new List<string>
            {
                "Check your internet connection",
                "Try again in a few moments",
                "Ensure Facebook services are not experiencing outages"
            },
            FacebookErrorType.RateLimited => new List<string>
            {
                "Wait 15-30 minutes before trying again",
                "Reduce the frequency of your posts",
                "Consider scheduling posts instead of publishing immediately"
            },
            FacebookErrorType.General => new List<string>
            {
                "Try publishing the post again",
                "Check if your post content meets Facebook's community standards",
                "Ensure your media files are in supported formats"
            },
            _ => new List<string> { "Try again", "Contact support if the problem persists" }
        };
    }

    private bool RequiresReconnection()
    {
        return PublishResult?.ErrorType == FacebookErrorType.TokenExpired ||
               PublishResult?.ErrorType == FacebookErrorType.TokenInvalid ||
               PublishResult?.ErrorType == FacebookErrorType.MissingPermissions ||
               PublishResult?.ErrorType == FacebookErrorType.ConnectionLost;
    }

    private bool CanRetry()
    {
        return PublishResult?.ErrorType == FacebookErrorType.General ||
               PublishResult?.ErrorType == FacebookErrorType.ConnectionLost ||
               PublishResult?.ErrorType == FacebookErrorType.RateLimited;
    }

    private async Task HandleReconnectClicked()
    {
        if (OnReconnectClicked.HasDelegate)
        {
            await OnReconnectClicked.InvokeAsync();
        }
        else
        {
            NavigationManager.NavigateTo("/facebook/connection");
        }
    }

    private async Task HandleRetryClicked()
    {
        if (OnRetryClicked.HasDelegate)
        {
            IsRetrying = true;
            StateHasChanged();
            
            try
            {
                await OnRetryClicked.InvokeAsync();
            }
            finally
            {
                IsRetrying = false;
                StateHasChanged();
            }
        }
    }

    private async Task HandleEditClicked()
    {
        if (OnEditClicked.HasDelegate)
        {
            await OnEditClicked.InvokeAsync();
        }
    }

    private async Task HandleDismissClicked()
    {
        ShowErrorAlert = false;
        StateHasChanged();
        
        if (OnDismissClicked.HasDelegate)
        {
            await OnDismissClicked.InvokeAsync();
        }
    }
}

<style>
    .post-publishing-error-handler .alert {
        border-left: 4px solid;
    }
    
    .post-publishing-error-handler .alert-danger {
        border-left-color: #dc3545;
    }
    
    .post-publishing-error-handler .alert-warning {
        border-left-color: #ffc107;
    }
    
    .post-publishing-error-handler .alert-info {
        border-left-color: #0dcaf0;
    }
    
    .post-publishing-error-handler details summary {
        font-size: 0.875rem;
    }
    
    .post-publishing-error-handler details[open] summary {
        margin-bottom: 0.5rem;
    }
    
    .post-publishing-error-handler ul {
        padding-left: 1.2rem;
    }
</style>
