{"id": "5b44c363-a591-4adb-989d-edb4f40eb823", "template": "app-nolayers", "versions": {"LeptonX": "4.1.1", "AbpFramework": "9.1.1", "AbpCommercial": "9.1.1", "AbpStudio": "0.9.26", "TargetDotnetFramework": "net9.0"}, "modules": {"FbAutoReplyPlatformExpress": {"path": "FbAutoReplyPlatformExpress.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": []}}, "creatingStudioConfiguration": {"template": "app-nolayers", "createdAbpStudioVersion": "0.9.26", "multiTenancy": "false", "runInstallLibs": "true", "useLocalReferences": "false", "uiFramework": "blazor-webapp", "databaseProvider": "ef", "runDbMigrator": "true", "databaseManagementSystem": "sqlserver", "createInitialMigration": "true", "theme": "leptonx", "themeStyle": "dim", "themeMenuPlacement": "side", "publicWebsite": "", "optionalModules": " TextTemplateManagement LanguageManagement AuditLogging OpenIddictAdmin", "socialLogin": "true", "createCommand": "abp new FbAutoReplyPlatformExpress -t app-nolayers --ui-framework blazor-webapp --mobile  --database-provider ef --database-management-system sqlserver --theme leptonx --no-tests --without-cms-kit --dont-run-bundling --no-multi-tenancy -no-saas -no-gdpr -no-file-management"}}