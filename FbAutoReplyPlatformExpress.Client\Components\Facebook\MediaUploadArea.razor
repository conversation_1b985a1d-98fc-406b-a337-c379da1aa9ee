@using FbAutoReplyPlatformExpress.Services.Dtos
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Logging
@inject IJSRuntime JSRuntime
@inject ILogger<MediaUploadArea> Logger

<div class="media-upload-area @(IsDragOver ? "drag-over" : "")" 
     @ondrop="OnDrop" 
     @ondragover="OnDragOver" 
     @ondragenter="OnDragEnter" 
     @ondragleave="OnDragLeave"
     @ondragover:preventDefault="true"
     @ondrop:preventDefault="true">
    
    @if (!Media.Any())
    {
        <!-- Empty State -->
        <div class="upload-placeholder">
            <Icon Name="@GetUploadIcon()" Size="IconSize.x3" class="text-muted mb-3" />
            <h6 class="text-muted">@GetUploadText()</h6>
            <p class="text-muted small mb-3">
                Drag and drop @(PostType == FacebookPostType.Image ? "images" : "video") here, or click to browse
            </p>
            <InputFile OnChange="OnFileSelected" 
                      multiple="@(PostType == FacebookPostType.Image)" 
                      accept="@GetAcceptedFileTypes()"
                      class="d-none" 
                      @ref="fileInput" />
            <Button Color="Color.Primary" Outline="true" @onclick="TriggerFileInput">
                <Icon Name="IconName.FileUpload" class="me-2" />
                Choose @(PostType == FacebookPostType.Image ? "Images" : "Video")
            </Button>
        </div>
    }
    else
    {
        <!-- Media Preview Grid -->
        <div class="media-preview-grid">
            @foreach (var (media, index) in Media.Select((m, i) => (m, i)))
            {
                <div class="media-preview-item">
                    <div class="media-preview-container">
                        @if (media.IsImage)
                        {
                            <img src="@GetMediaDataUrl(media)" alt="Preview" class="media-preview-image" />
                        }
                        else if (media.IsVideo)
                        {
                            <video class="media-preview-video" controls>
                                <source src="@GetMediaDataUrl(media)" type="@media.ContentType" />
                                Your browser does not support the video tag.
                            </video>
                        }
                        
                        <!-- Remove Button -->
                        <Button Color="Color.Danger" 
                               Size="Size.Small" 
                               class="media-remove-btn"
                               @onclick="() => RemoveMedia(index)">
                            <Icon Name="IconName.Times" />
                        </Button>
                        
                        <!-- File Info -->
                        <div class="media-info">
                            <small class="text-muted">
                                @media.FileName (@FormatFileSize(media.Size))
                            </small>
                        </div>
                    </div>
                </div>
            }
            
            <!-- Add More Button (for images only) -->
            @if (PostType == FacebookPostType.Image && Media.Count < MaxImageCount)
            {
                <div class="media-preview-item add-more-item">
                    <div class="add-more-container" @onclick="TriggerFileInput">
                        <Icon Name="IconName.Add" Size="IconSize.x2" class="text-muted" />
                        <small class="text-muted">Add More</small>
                    </div>
                </div>
            }
        </div>
        
        <!-- File Input (Hidden) -->
        <InputFile OnChange="OnFileSelected" 
                  multiple="@(PostType == FacebookPostType.Image)" 
                  accept="@GetAcceptedFileTypes()"
                  class="d-none" 
                  @ref="fileInput" />
    }
    
    <!-- Upload Progress -->
    @if (IsUploading)
    {
        <div class="upload-progress mt-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="text-muted">Uploading...</span>
                <span class="text-muted">@UploadProgress%</span>
            </div>
            <Progress Value="UploadProgress" />
        </div>
    }
    
    <!-- Error Messages -->
    @if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <Alert Color="Color.Danger" class="mt-3">
            <Icon Name="IconName.ExclamationTriangle" class="me-2" />
            @ErrorMessage
        </Alert>
    }
</div>

@code {
    [Parameter] public List<FacebookPostMediaDto> Media { get; set; } = new();
    [Parameter] public EventCallback<List<FacebookPostMediaDto>> MediaChanged { get; set; }
    [Parameter] public FacebookPostType PostType { get; set; }
    [Parameter] public EventCallback OnMediaChanged { get; set; }

    private const int MaxImageCount = 10;
    private const int MaxVideoCount = 1;
    private const long MaxImageSize = 10 * 1024 * 1024; // 10MB
    private const long MaxVideoSize = 100 * 1024 * 1024; // 100MB

    private InputFile? fileInput;
    private bool IsDragOver = false;
    private bool IsUploading = false;
    private int UploadProgress = 0;
    private string? ErrorMessage;

    private IconName GetUploadIcon()
    {
        return PostType switch
        {
            FacebookPostType.Image => IconName.Image,
            FacebookPostType.Video => IconName.Video,
            _ => IconName.FileUpload
        };
    }

    private string GetUploadText()
    {
        return PostType switch
        {
            FacebookPostType.Image => "Add Images",
            FacebookPostType.Video => "Add Video",
            _ => "Add Media"
        };
    }

    private string GetAcceptedFileTypes()
    {
        return PostType switch
        {
            FacebookPostType.Image => "image/*",
            FacebookPostType.Video => "video/*",
            _ => "*/*"
        };
    }

    private async Task TriggerFileInput()
    {
        if (fileInput != null)
        {
            await JSRuntime.InvokeVoidAsync("triggerFileInput", fileInput.Element);
        }
    }

    private async Task OnFileSelected(InputFileChangeEventArgs e)
    {
        ErrorMessage = null;
        
        try
        {
            var files = e.GetMultipleFiles(PostType == FacebookPostType.Image ? MaxImageCount : MaxVideoCount);
            
            if (!files.Any()) return;

            IsUploading = true;
            UploadProgress = 0;
            StateHasChanged();

            var newMedia = new List<FacebookPostMediaDto>();
            var totalFiles = files.Count();
            var processedFiles = 0;

            foreach (var file in files)
            {
                // Validate file
                if (!ValidateFile(file))
                {
                    continue;
                }

                // Read file content
                var buffer = new byte[file.Size];
                await file.OpenReadStream(MaxVideoSize).ReadAsync(buffer);

                var mediaDto = new FacebookPostMediaDto
                {
                    FileName = file.Name,
                    ContentType = file.ContentType,
                    Content = buffer,
                    Size = file.Size
                };

                newMedia.Add(mediaDto);
                
                processedFiles++;
                UploadProgress = (int)((double)processedFiles / totalFiles * 100);
                StateHasChanged();
            }

            // Add to existing media (for images) or replace (for video)
            if (PostType == FacebookPostType.Video)
            {
                Media.Clear();
            }
            
            Media.AddRange(newMedia);
            await NotifyMediaChanged();
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "Error during file upload process");

            if (ex is ArgumentException || ex.Message.Contains("size"))
            {
                ErrorMessage = "One or more files exceed the maximum size limit. Please choose smaller files.";
            }
            else if (ex.Message.Contains("format") || ex.Message.Contains("type"))
            {
                ErrorMessage = "One or more files are in an unsupported format. Please choose valid image or video files.";
            }
            else
            {
                ErrorMessage = "An error occurred while uploading files. Please try again.";
            }
        }
        finally
        {
            IsUploading = false;
            UploadProgress = 0;
            StateHasChanged();
        }
    }

    private bool ValidateFile(IBrowserFile file)
    {
        // Check file type
        if (PostType == FacebookPostType.Image && !file.ContentType.StartsWith("image/"))
        {
            ErrorMessage = "Only image files are allowed for image posts.";
            return false;
        }
        
        if (PostType == FacebookPostType.Video && !file.ContentType.StartsWith("video/"))
        {
            ErrorMessage = "Only video files are allowed for video posts.";
            return false;
        }

        // Check file size
        var maxSize = PostType == FacebookPostType.Image ? MaxImageSize : MaxVideoSize;
        if (file.Size > maxSize)
        {
            ErrorMessage = $"File size must be less than {FormatFileSize(maxSize)}.";
            return false;
        }

        // Check count limits
        if (PostType == FacebookPostType.Image && Media.Count >= MaxImageCount)
        {
            ErrorMessage = $"Maximum {MaxImageCount} images allowed.";
            return false;
        }
        
        if (PostType == FacebookPostType.Video && Media.Count >= MaxVideoCount)
        {
            ErrorMessage = "Only one video allowed per post.";
            return false;
        }

        return true;
    }

    private async Task RemoveMedia(int index)
    {
        if (index >= 0 && index < Media.Count)
        {
            Media.RemoveAt(index);
            await NotifyMediaChanged();
        }
    }

    private string GetMediaDataUrl(FacebookPostMediaDto media)
    {
        return $"data:{media.ContentType};base64,{Convert.ToBase64String(media.Content)}";
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private async Task NotifyMediaChanged()
    {
        await MediaChanged.InvokeAsync(Media);
        await OnMediaChanged.InvokeAsync();
    }

    // Drag and Drop Events
    private void OnDragEnter(DragEventArgs e)
    {
        IsDragOver = true;
    }

    private void OnDragLeave(DragEventArgs e)
    {
        IsDragOver = false;
    }

    private void OnDragOver(DragEventArgs e)
    {
        IsDragOver = true;
    }

    private async Task OnDrop(DragEventArgs e)
    {
        IsDragOver = false;
        
        // Handle dropped files
        if (e.DataTransfer?.Files?.Length > 0)
        {
            await JSRuntime.InvokeVoidAsync("handleFileDrop", fileInput?.Element, e.DataTransfer.Files);
        }
    }
}

<style>
    .media-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
        min-height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .media-upload-area.drag-over {
        border-color: #007bff;
        background-color: rgba(0, 123, 255, 0.1);
    }

    .upload-placeholder {
        width: 100%;
    }

    .media-preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        width: 100%;
    }

    .media-preview-item {
        position: relative;
        aspect-ratio: 1;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #dee2e6;
    }

    .media-preview-container {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .media-preview-image,
    .media-preview-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .media-remove-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .media-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px;
        font-size: 0.75rem;
    }

    .add-more-item {
        border: 2px dashed #dee2e6;
        background: transparent;
    }

    .add-more-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .add-more-container:hover {
        background-color: rgba(0, 123, 255, 0.1);
        border-color: #007bff;
    }

    .upload-progress {
        width: 100%;
    }
</style>

<script>
    window.triggerFileInput = (element) => {
        if (element) {
            element.click();
        }
    };

    window.handleFileDrop = (fileInput, files) => {
        if (fileInput && files) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
    };
</script>
